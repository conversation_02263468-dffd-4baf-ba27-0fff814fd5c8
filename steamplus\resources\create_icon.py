#!/usr/bin/env python3
"""
创建简单的应用图标
"""

from pathlib import Path
from PySide6.QtGui import QPixmap, QPainter, QBrush, QPen, QFont
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QApplication
import sys


def create_icon():
    """创建应用图标"""
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # 创建 32x32 的图标
    size = 32
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # 绘制背景圆形
    painter.setBrush(QBrush(Qt.blue))
    painter.setPen(QPen(Qt.darkBlue, 2))
    painter.drawEllipse(2, 2, size-4, size-4)
    
    # 绘制文字 "S+"
    painter.setPen(QPen(Qt.white))
    font = QFont("Arial", 12, QFont.Bold)
    painter.setFont(font)
    painter.drawText(pixmap.rect(), Qt.<PERSON>gn<PERSON>enter, "S+")
    
    painter.end()
    
    # 保存图标
    icon_path = Path(__file__).parent / "icon.png"
    pixmap.save(str(icon_path))
    print(f"Icon created: {icon_path}")
    
    return icon_path


if __name__ == "__main__":
    create_icon()
