#!/usr/bin/env python3
"""
测试开箱可用连接功能
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))


async def test_connection_strategies():
    """测试各种连接策略"""
    print("🧪 测试 SteamPlus 智能连接策略")
    print("=" * 50)
    
    try:
        from steamplus.service.events import event_bus
        from steamplus.service.steam import steam_connection
        from steamplus.service.standalone_translate import standalone_translate_service
        
        # 初始化事件系统
        print("📋 初始化事件系统...")
        await event_bus.initialize()
        
        # 测试 Steam 连接
        print("\n🔍 测试 Steam 智能连接...")
        success = await steam_connection.connect()
        
        if success:
            mode = steam_connection._connection_mode
            print(f"✅ Steam 连接成功！模式: {mode}")
            
            if mode == "cdp":
                print("   🎯 CDP 模式 - 可以直接注入 Steam 页面")
                print(f"   📡 调试端口: {steam_connection._debug_port}")
                print(f"   📄 已连接页面: {len(steam_connection.pages)}")
            elif mode == "browser":
                print("   🌐 浏览器模式 - 独立浏览器窗口已打开")
            elif mode == "standalone":
                print("   🔤 独立模式 - 剪贴板翻译功能可用")
        else:
            print("❌ Steam 连接失败")
        
        # 测试独立翻译服务
        print("\n🔤 测试独立翻译服务...")
        await standalone_translate_service.initialize()
        
        status = standalone_translate_service.get_status()
        print(f"   运行状态: {'✅' if status['running'] else '❌'}")
        print(f"   剪贴板监控: {'✅' if status['clipboard_monitor'] else '❌'}")
        print(f"   剪贴板可用: {'✅' if status['clipboard_available'] else '❌'}")
        print(f"   可用翻译引擎: {', '.join(status['providers'])}")
        
        # 测试翻译功能
        print("\n🌐 测试翻译功能...")
        test_texts = [
            "Hello World",
            "Good morning",
            "How are you today?"
        ]
        
        for text in test_texts:
            print(f"   翻译: '{text}'")
            result = await standalone_translate_service.translate_text(text)
            
            if result['success']:
                print(f"   结果: '{result['translation']}'")
                print(f"   引擎: {result['provider']}")
            else:
                print(f"   失败: {result.get('error', '未知错误')}")
            print()
        
        # 显示翻译历史
        history = standalone_translate_service.get_translation_history(5)
        if history:
            print("📚 最近翻译历史:")
            for i, item in enumerate(history, 1):
                print(f"   {i}. {item['original'][:30]}... → {item['translation'][:30]}...")
        
        print("\n🎉 测试完成！")
        
        # 显示使用建议
        print("\n💡 使用建议:")
        if steam_connection._connection_mode == "cdp":
            print("   - Steam 页面翻译已就绪，在 Steam 中选中文本即可翻译")
        elif steam_connection._connection_mode == "browser":
            print("   - 在打开的浏览器窗口中访问 Steam 网站")
        
        if status['clipboard_available']:
            print("   - 复制英文文本到剪贴板会自动翻译")
        
        print("   - 运行 'python start_easy.py' 启动完整服务")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        print("\n🧹 清理测试资源...")
        try:
            await standalone_translate_service.cleanup()
            await steam_connection.disconnect()
            await event_bus.cleanup()
        except:
            pass


def main():
    """主函数"""
    try:
        asyncio.run(test_connection_strategies())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")


if __name__ == "__main__":
    main()
