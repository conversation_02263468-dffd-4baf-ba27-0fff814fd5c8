# SteamPlus 用户指南

## 🎮 欢迎使用 SteamPlus！

SteamPlus 是一个智能的 Steam 翻译工具，现在支持**开箱可用**的连接方式，无需复杂配置！

## 🚀 快速开始

### 方式1: 开箱可用模式（推荐）

```bash
# 下载项目后，直接运行：
python start_easy.py
```

**特点**：
- ✅ 零配置，自动处理一切
- ✅ 智能检测和连接 Steam
- ✅ 自动安装缺失依赖
- ✅ 多种备用连接方案
- ✅ 友好的用户界面

### 方式2: 修复版启动

```bash
# 如果遇到问题，使用修复版：
python start_fixed.py
```

### 方式3: 仅 API 服务

```bash
# 只需要翻译 API 功能：
python start_fixed.py --api-only
```

## 🔧 功能说明

### 🎯 Steam 页面翻译
- **自动模式**：程序会自动重启 Steam 并启用调试模式
- **使用方法**：在 Steam 页面中选中英文文本，等待翻译结果显示
- **支持页面**：Steam 商店、社区、库页面

### 📋 剪贴板翻译
- **自动监控**：复制英文文本到剪贴板会自动翻译
- **智能过滤**：自动识别需要翻译的内容
- **通知显示**：翻译结果通过系统通知显示

### 🌐 API 翻译服务
- **本地服务**：http://localhost:50055
- **API 文档**：http://localhost:50055/docs
- **支持集成**：其他应用可以调用翻译接口

### 🖥️ 系统托盘
- **状态显示**：实时显示连接状态
- **快速控制**：右键菜单控制翻译开关
- **设置界面**：完整的设置对话框

## 📱 使用模式

### 模式1: 完整 Steam 集成 🎯
```
✅ Steam 自动重启并启用调试模式
✅ 页面翻译功能
✅ 剪贴板翻译
✅ API 服务
✅ 系统托盘
```

### 模式2: 独立浏览器 🌐
```
✅ 独立浏览器窗口
✅ Steam 网站翻译
✅ 剪贴板翻译
✅ API 服务
✅ 系统托盘
```

### 模式3: 纯翻译服务 🔤
```
✅ 剪贴板翻译
✅ API 服务
✅ 系统托盘
⚠️  无 Steam 页面集成
```

## 🔍 智能连接策略

程序会按以下顺序尝试连接：

1. **检测现有调试端口** - 如果 Steam 已开启调试模式
2. **自动启动调试模式** - 重启 Steam 并添加调试参数
3. **独立浏览器模式** - 启动独立浏览器访问 Steam
4. **纯翻译模式** - 提供基础翻译功能

**无论哪种情况，都能保证翻译功能可用！**

## 🎛️ 翻译引擎

### 支持的引擎
- **Bing 翻译**（默认）- 免费，无需配置
- **Google 翻译** - 免费，无需配置
- **DeepL 翻译** - 需要 API 密钥
- **其他引擎** - 可通过插件扩展

### 配置 API 密钥
1. 启动程序后，右键系统托盘图标
2. 选择"设置" → "翻译"标签页
3. 选择翻译引擎并输入 API 密钥
4. 点击"保存"

## 📋 常见问题

### Q: 程序启动后看不到界面？
A: 程序以系统托盘方式运行，查看任务栏右下角的托盘图标。

### Q: Steam 连接失败怎么办？
A: 不用担心！程序会自动启用剪贴板翻译模式，功能完全可用。

### Q: 如何使用剪贴板翻译？
A: 复制任何英文文本到剪贴板，程序会自动检测并翻译，结果通过通知显示。

### Q: 翻译结果不准确？
A: 可以在设置中切换不同的翻译引擎，或配置高质量的 API 密钥。

### Q: 如何退出程序？
A: 右键系统托盘图标，选择"退出"，或按 Ctrl+C（命令行模式）。

## 🔧 高级功能

### API 使用示例

```bash
# 翻译文本
curl -X POST http://localhost:50055/translate \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello World", "target_lang": "zh-CN"}'

# 查看状态
curl http://localhost:50055/status

# 获取翻译提供商
curl http://localhost:50055/translate/providers
```

### 插件开发

```python
from steamplus.plugins import BasePlugin

class MyPlugin(BasePlugin):
    name = "my_plugin"
    version = "1.0.0"
    
    async def initialize(self):
        print("我的插件已加载！")
    
    async def on_translation(self, text: str, result: str):
        print(f"翻译完成: {text} -> {result}")
```

## 🛠️ 故障排除

### 依赖问题
```bash
# 安装核心依赖
pip install loguru click pydantic fastapi uvicorn aiohttp psutil keyring

# 安装可选依赖
pip install PySide6 playwright pyperclip win10toast
```

### 权限问题
- 确保有管理员权限（重启 Steam 需要）
- 检查防火墙设置（API 服务需要）

### Steam 问题
- 确保 Steam 已安装且可正常运行
- 如果自动重启失败，可手动添加启动参数：`--remote-debugging-port=9222`

## 📊 性能优化

### 减少资源占用
- 关闭不需要的翻译引擎
- 调整剪贴板监控频率
- 限制翻译历史记录数量

### 提高翻译速度
- 使用本地翻译引擎
- 启用翻译缓存
- 配置高速 API 服务

## 🎉 享受翻译！

现在您可以：
- 🎮 在 Steam 中畅快游戏，遇到英文直接翻译
- 📋 复制任何英文内容，自动获得翻译
- 🌐 通过 API 集成到其他应用
- 🔧 通过插件扩展更多功能

**祝您使用愉快！** 🚀

---

## 📞 支持与反馈

- 🐛 问题报告：[GitHub Issues](https://github.com/steamplus/steamplus/issues)
- 💡 功能建议：[GitHub Discussions](https://github.com/steamplus/steamplus/discussions)
- 📖 完整文档：查看项目中的其他 .md 文件
