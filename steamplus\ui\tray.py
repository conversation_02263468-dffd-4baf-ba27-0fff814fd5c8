"""
系统托盘界面

基于 PySide6 的系统托盘界面，提供翻译开关、设置、状态显示等功能。
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QApplication, QSystemTrayIcon, QMenu, QMessageBox, 
    QWidget, QVBoxLayout, QLabel, QProgressBar
)
from PySide6.QtCore import QTimer, QThread, Signal, QObject, Qt
from PySide6.QtGui import QIcon, QAction, QPixmap
from loguru import logger

from ..service.events import event_bus, Events
from ..service.settings import get_settings
from ..service.steam import steam_connection
from .settings_dialog import SettingsDialog


class EventHandler(QObject):
    """事件处理器，用于在主线程中处理异步事件"""
    
    steam_connected = Signal()
    steam_disconnected = Signal()
    translation_completed = Signal(dict)
    settings_changed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # 订阅事件
        self._setup_event_subscriptions()
    
    def _setup_event_subscriptions(self):
        """设置事件订阅"""
        try:
            event_bus.subscribe(Events.STEAM_CONNECTED, self._on_steam_connected)
            event_bus.subscribe(Events.STEAM_DISCONNECTED, self._on_steam_disconnected)
            event_bus.subscribe(Events.TRANSLATION_COMPLETED, self._on_translation_completed)
            event_bus.subscribe(Events.SETTINGS_CHANGED, self._on_settings_changed)
        except Exception as e:
            logger.error(f"Failed to setup event subscriptions: {e}")
    
    async def _on_steam_connected(self, event):
        """Steam 连接事件"""
        self.steam_connected.emit()
    
    async def _on_steam_disconnected(self, event):
        """Steam 断开事件"""
        self.steam_disconnected.emit()
    
    async def _on_translation_completed(self, event):
        """翻译完成事件"""
        self.translation_completed.emit(event.data if event.data else {})
    
    async def _on_settings_changed(self, event):
        """设置变更事件"""
        self.settings_changed.emit(event.data.model_dump() if event.data else {})


class SystemTrayIcon(QSystemTrayIcon):
    """系统托盘图标"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.settings_dialog: Optional[SettingsDialog] = None
        self.event_handler = EventHandler()
        self.translation_enabled = True
        self.steam_connected = False
        
        # 设置图标
        self.setIcon(self._create_icon())
        
        # 创建菜单
        self.create_menu()
        
        # 连接信号
        self.activated.connect(self.on_tray_activated)
        self.event_handler.steam_connected.connect(self.on_steam_connected)
        self.event_handler.steam_disconnected.connect(self.on_steam_disconnected)
        self.event_handler.translation_completed.connect(self.on_translation_completed)
        self.event_handler.settings_changed.connect(self.on_settings_changed)
        
        # 设置提示文本
        self.setToolTip("SteamPlus - Steam 即时翻译")
        
        # 显示启动消息
        if self.isSystemTrayAvailable():
            self.showMessage(
                "SteamPlus",
                "SteamPlus 已启动，正在等待 Steam 连接...",
                QSystemTrayIcon.MessageIcon.Information,
                3000
            )
        
        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次
    
    def _create_icon(self) -> QIcon:
        """创建托盘图标"""
        try:
            # 尝试加载自定义图标
            icon_path = Path(__file__).parent.parent / "resources" / "icon.png"
            if icon_path.exists():
                return QIcon(str(icon_path))
        except Exception as e:
            logger.warning(f"Failed to load custom icon: {e}")

        try:
            # 创建一个简单的默认图标
            from PySide6.QtGui import QPixmap, QPainter, QBrush, QPen, QFont
            from PySide6.QtCore import Qt

            # 创建一个 32x32 的图标
            size = 32
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 绘制背景圆形
            painter.setBrush(QBrush(Qt.GlobalColor.blue))
            painter.setPen(QPen(Qt.GlobalColor.darkBlue, 2))
            painter.drawEllipse(2, 2, size-4, size-4)

            # 绘制文字 "S+"
            painter.setPen(QPen(Qt.GlobalColor.white))
            font = QFont("Arial", 12, QFont.Weight.Bold)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "S+")

            painter.end()

            return QIcon(pixmap)

        except Exception as e:
            logger.error(f"Failed to create icon: {e}")
            # 最后的备用方案：返回空图标
            return QIcon()
    
    def create_menu(self):
        """创建右键菜单"""
        menu = QMenu()
        
        # 翻译开关
        self.toggle_action = QAction("禁用翻译", self)
        self.toggle_action.triggered.connect(self.toggle_translation)
        menu.addAction(self.toggle_action)
        
        menu.addSeparator()
        
        # Steam 连接状态
        self.status_action = QAction("Steam: 未连接", self)
        self.status_action.setEnabled(False)
        menu.addAction(self.status_action)
        
        # Steam 连接控制
        self.connect_action = QAction("连接 Steam", self)
        self.connect_action.triggered.connect(self.toggle_steam_connection)
        menu.addAction(self.connect_action)
        
        menu.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        menu.addAction(settings_action)
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        menu.addAction(about_action)
        
        menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application)
        menu.addAction(quit_action)
        
        self.setContextMenu(menu)
    
    def on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_settings()
    
    def toggle_translation(self):
        """切换翻译功能"""
        self.translation_enabled = not self.translation_enabled
        
        if self.translation_enabled:
            self.toggle_action.setText("禁用翻译")
            self.showMessage("SteamPlus", "翻译功能已启用", 
                           QSystemTrayIcon.MessageIcon.Information, 2000)
        else:
            self.toggle_action.setText("启用翻译")
            self.showMessage("SteamPlus", "翻译功能已禁用", 
                           QSystemTrayIcon.MessageIcon.Warning, 2000)
    
    def toggle_steam_connection(self):
        """切换 Steam 连接"""
        try:
            if self.steam_connected:
                # 断开连接
                asyncio.create_task(steam_connection.disconnect())
            else:
                # 连接
                asyncio.create_task(steam_connection.connect())
        except Exception as e:
            logger.error(f"Failed to toggle Steam connection: {e}")
            self.showMessage("SteamPlus", f"连接操作失败: {e}", 
                           QSystemTrayIcon.MessageIcon.Critical, 3000)
    
    def show_settings(self):
        """显示设置对话框"""
        try:
            if not self.settings_dialog:
                self.settings_dialog = SettingsDialog()
            
            self.settings_dialog.show()
            self.settings_dialog.raise_()
            self.settings_dialog.activateWindow()
        except Exception as e:
            logger.error(f"Failed to show settings dialog: {e}")
            QMessageBox.critical(None, "错误", f"无法打开设置对话框: {e}")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            None,
            "关于 SteamPlus",
            """
            <h3>SteamPlus v0.1.0</h3>
            <p>Steam 即时翻译工具 - Python 重构版</p>
            <p>支持划词翻译、多引擎切换、系统托盘等功能</p>
            <br>
            <p><b>功能特性:</b></p>
            <ul>
            <li>🚀 即时翻译</li>
            <li>🔄 多引擎切换</li>
            <li>🎯 浏览器级注入</li>
            <li>🖥️ 系统托盘</li>
            <li>🔧 插件化架构</li>
            </ul>
            <br>
            <p><a href="https://github.com/steamplus/steamplus">GitHub 项目主页</a></p>
            """
        )
    
    def quit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            None,
            "确认退出",
            "确定要退出 SteamPlus 吗？\n\n退出后翻译功能将停止工作。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清理资源
            self.cleanup()
            QApplication.quit()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.status_timer:
                self.status_timer.stop()
            
            if self.settings_dialog:
                self.settings_dialog.close()
                self.settings_dialog = None
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def update_status(self):
        """更新状态"""
        try:
            # 更新 Steam 连接状态
            connected = steam_connection.is_connected
            if connected != self.steam_connected:
                self.steam_connected = connected
                if connected:
                    self.on_steam_connected()
                else:
                    self.on_steam_disconnected()
        except Exception as e:
            logger.error(f"Error updating status: {e}")
    
    def on_steam_connected(self):
        """Steam 连接成功"""
        self.steam_connected = True
        self.status_action.setText("Steam: 已连接")
        self.connect_action.setText("断开 Steam")
        
        # 更新图标（可以换成绿色图标表示连接）
        self.setToolTip("SteamPlus - Steam 已连接，翻译功能就绪")
        
        self.showMessage(
            "SteamPlus",
            "Steam 连接成功，翻译功能已就绪",
            QSystemTrayIcon.MessageIcon.Information,
            3000
        )
    
    def on_steam_disconnected(self):
        """Steam 连接断开"""
        self.steam_connected = False
        self.status_action.setText("Steam: 未连接")
        self.connect_action.setText("连接 Steam")
        
        # 更新图标
        self.setToolTip("SteamPlus - Steam 未连接")
        
        self.showMessage(
            "SteamPlus",
            "Steam 连接断开，正在尝试重新连接...",
            QSystemTrayIcon.MessageIcon.Warning,
            3000
        )
    
    def on_translation_completed(self, data):
        """翻译完成"""
        if not self.translation_enabled:
            return
        
        # 可以在这里显示翻译通知（可选）
        # 避免过于频繁的通知
        pass
    
    def on_settings_changed(self, data):
        """设置变更"""
        try:
            # 根据设置更新界面状态
            if 'translate' in data:
                translate_settings = data['translate']
                if 'provider' in translate_settings:
                    logger.info(f"Translation provider changed to: {translate_settings['provider']}")
        except Exception as e:
            logger.error(f"Error handling settings change: {e}")


class TrayApplication:
    """托盘应用程序"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.tray_icon: Optional[SystemTrayIcon] = None
        self.running = False
    
    def start(self) -> bool:
        """启动托盘应用"""
        if self.running:
            return True
        
        try:
            # 创建 QApplication
            self.app = QApplication.instance()
            if not self.app:
                self.app = QApplication(sys.argv)
            
            # 设置应用程序属性
            self.app.setQuitOnLastWindowClosed(False)
            self.app.setApplicationName("SteamPlus")
            self.app.setApplicationVersion("0.1.0")
            
            # 检查系统托盘支持
            if not QSystemTrayIcon.isSystemTrayAvailable():
                QMessageBox.critical(
                    None,
                    "系统托盘",
                    "系统不支持托盘功能，请检查系统设置"
                )
                return False
            
            # 创建托盘图标
            self.tray_icon = SystemTrayIcon()
            self.tray_icon.show()
            
            self.running = True
            logger.info("System tray started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system tray: {e}")
            return False
    
    def stop(self):
        """停止托盘应用"""
        if not self.running:
            return
        
        try:
            if self.tray_icon:
                self.tray_icon.cleanup()
                self.tray_icon.hide()
                self.tray_icon = None
            
            self.running = False
            logger.info("System tray stopped")
            
        except Exception as e:
            logger.error(f"Error stopping system tray: {e}")
    
    def exec(self) -> int:
        """运行应用程序事件循环"""
        if self.app and self.running:
            return self.app.exec()
        return 0


# 全局托盘应用实例
tray_app = TrayApplication()
