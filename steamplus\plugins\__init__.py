"""
SteamPlus 插件系统

支持第三方功能扩展的插件架构。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional


class BasePlugin(ABC):
    """插件基类"""
    
    name: str = ""
    version: str = "1.0.0"
    description: str = ""
    author: str = ""
    
    def __init__(self):
        self.enabled = True
        self.config: Dict[str, Any] = {}
    
    @abstractmethod
    async def initialize(self) -> None:
        """插件初始化"""
        pass
    
    async def cleanup(self) -> None:
        """插件清理"""
        pass
    
    async def on_translation(self, text: str, result: str) -> None:
        """翻译完成时的回调"""
        pass
    
    async def on_steam_connected(self) -> None:
        """Steam 连接时的回调"""
        pass
    
    async def on_steam_disconnected(self) -> None:
        """Steam 断开时的回调"""
        pass


__all__ = ["BasePlugin"]
