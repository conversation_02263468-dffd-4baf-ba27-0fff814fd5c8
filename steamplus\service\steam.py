"""
Steam 连接管理模块

多种连接策略：
1. 自动启动 Steam 调试模式
2. 检测现有调试端口
3. 浏览器扩展模式
4. 独立翻译模式
"""

import asyncio
import json
import subprocess
import time
import os
from typing import Dict, List, Optional, Set
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from loguru import logger

from .events import event_bus, Events
from .settings import get_settings
from .machine import ProcessUtils


class SteamConnection:
    """Steam 连接管理器 - 开箱可用版本"""

    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.pages: Dict[str, Page] = {}
        self.playwright = None

        self._connected = False
        self._connecting = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._debug_port: Optional[int] = None
        self._connection_mode = "none"  # none, cdp, browser, standalone

        # 支持的页面类型
        self.supported_pages = {
            'store': ['store.steampowered.com'],
            'community': ['steamcommunity.com'],
            'library': ['steamloopback.host'],
        }

        # Steam 安装路径检测
        self.steam_paths = [
            Path("C:/Program Files (x86)/Steam/Steam.exe"),
            Path("C:/Program Files/Steam/Steam.exe"),
            Path(os.path.expanduser("~/Steam/Steam.exe")),
        ]
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    async def connect(self) -> bool:
        """智能连接到 Steam - 多种策略"""
        if self._connected or self._connecting:
            return self._connected

        self._connecting = True

        try:
            logger.info("🔍 开始智能连接 Steam...")

            # 策略1: 检测现有调试端口
            if await self._try_existing_debug_port():
                self._connection_mode = "cdp"
                logger.info("✅ 使用现有调试端口连接成功")
                return True

            # 策略2: 自动启动 Steam 调试模式
            if await self._try_auto_start_steam_debug():
                self._connection_mode = "cdp"
                logger.info("✅ 自动启动调试模式连接成功")
                return True

            # 策略3: 启动独立浏览器模式
            if await self._try_standalone_browser():
                self._connection_mode = "browser"
                logger.info("✅ 独立浏览器模式连接成功")
                return True

            # 策略4: 纯翻译模式（无需 Steam 连接）
            await self._enable_standalone_mode()
            self._connection_mode = "standalone"
            logger.info("✅ 启用独立翻译模式")
            return True

        except Exception as e:
            logger.error(f"所有连接策略都失败: {e}")
            # 即使连接失败，也启用独立模式
            await self._enable_standalone_mode()
            self._connection_mode = "standalone"
            return True
        finally:
            self._connecting = False
    
    async def disconnect(self) -> None:
        """断开连接"""
        if not self._connected:
            return
        
        logger.info("Disconnecting from Steam...")
        
        self._connected = False
        
        # 停止监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        # 清理页面
        self.pages.clear()
        
        # 关闭浏览器连接
        if self.browser:
            try:
                await self.browser.close()
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
            self.browser = None
        
        # 停止 Playwright
        if self.playwright:
            try:
                await self.playwright.stop()
            except Exception as e:
                logger.warning(f"Error stopping playwright: {e}")
            self.playwright = None
        
        # 发布断开事件
        await event_bus.publish(Events.STEAM_DISCONNECTED)
        
        logger.info("Disconnected from Steam")
    
    async def _discover_existing_pages(self) -> None:
        """发现现有页面"""
        if not self.context:
            return
        
        try:
            pages = self.context.pages
            for page in pages:
                await self._handle_page(page)
        except Exception as e:
            logger.error(f"Failed to discover existing pages: {e}")
    
    async def _on_page_created(self, page: Page) -> None:
        """页面创建事件处理"""
        try:
            await self._handle_page(page)
        except Exception as e:
            logger.error(f"Error handling new page: {e}")
    
    async def _handle_page(self, page: Page) -> None:
        """处理页面"""
        try:
            url = page.url
            if not url:
                return
            
            # 检查是否是支持的页面类型
            page_type = self._get_page_type(url)
            if not page_type:
                return
            
            page_id = f"{page_type}_{id(page)}"
            self.pages[page_id] = page
            
            # 注入翻译脚本
            await self._inject_translation_script(page, page_type)
            
            # 监听页面关闭
            page.on("close", lambda: self._on_page_closed(page_id))
            
            logger.info(f"Handled {page_type} page: {url}")
            
            # 发布页面加载事件
            await event_bus.publish(Events.STEAM_PAGE_LOADED, {
                'page_id': page_id,
                'page_type': page_type,
                'url': url
            })
            
        except Exception as e:
            logger.error(f"Failed to handle page: {e}")
    
    def _get_page_type(self, url: str) -> Optional[str]:
        """获取页面类型"""
        for page_type, domains in self.supported_pages.items():
            for domain in domains:
                if domain in url:
                    return page_type
        return None
    
    def _on_page_closed(self, page_id: str) -> None:
        """页面关闭事件处理"""
        if page_id in self.pages:
            del self.pages[page_id]
            logger.debug(f"Page closed: {page_id}")
    
    async def _inject_translation_script(self, page: Page, page_type: str) -> None:
        """注入翻译脚本"""
        try:
            # 读取翻译脚本
            script_path = Path(__file__).parent.parent / "resources" / "inject.js"
            if not script_path.exists():
                logger.warning(f"Translation script not found: {script_path}")
                return
            
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            # 注入脚本
            await page.add_init_script(script_content)
            
            # 设置消息处理器
            await page.expose_function("steamplus_translate", self._handle_translation_request)
            
            logger.debug(f"Translation script injected into {page_type} page")
            
        except Exception as e:
            logger.error(f"Failed to inject translation script: {e}")
    
    async def _handle_translation_request(self, data: Dict) -> Dict:
        """处理翻译请求"""
        try:
            text = data.get('text', '')
            source_lang = data.get('source', 'auto')
            target_lang = data.get('target', 'zh-CN')

            if not text:
                return {'error': 'No text provided'}

            # 发布翻译开始事件
            await event_bus.publish(Events.TRANSLATION_STARTED, {
                'text': text,
                'source_lang': source_lang,
                'target_lang': target_lang
            })

            # 调用翻译服务
            from .translate import translation_service

            translation_result = await translation_service.translate(
                text=text,
                source_lang=source_lang,
                target_lang=target_lang
            )

            # 发布翻译完成事件
            await event_bus.publish(Events.TRANSLATION_COMPLETED, {
                'text': text,
                'result': translation_result.result,
                'provider': translation_result.provider,
                'duration': translation_result.duration
            })

            return {
                'result': translation_result.result,
                'source_lang': translation_result.source_lang,
                'target_lang': translation_result.target_lang,
                'provider': translation_result.provider,
                'confidence': translation_result.confidence,
                'cached': translation_result.cached,
                'duration': translation_result.duration
            }

        except Exception as e:
            logger.error(f"Translation request failed: {e}")
            await event_bus.publish(Events.TRANSLATION_FAILED, {
                'text': data.get('text', ''),
                'error': str(e)
            })
            return {'error': str(e)}
    
    async def _monitor_connection(self) -> None:
        """监控连接状态"""
        logger.info("Connection monitor started")
        
        while self._connected:
            try:
                # 检查浏览器连接状态
                if self.browser and not self.browser.is_connected():
                    logger.warning("Browser connection lost")
                    await self.disconnect()
                    break
                
                # 检查 Steam 进程状态
                if not ProcessUtils.find_steam_process():
                    logger.warning("Steam process not found")
                    await self.disconnect()
                    break
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection monitor: {e}")
                await asyncio.sleep(5)
        
        logger.info("Connection monitor stopped")
    
    def get_pages(self, page_type: Optional[str] = None) -> List[Page]:
        """获取页面列表"""
        if page_type:
            return [page for page_id, page in self.pages.items() 
                   if page_id.startswith(f"{page_type}_")]
        return list(self.pages.values())
    
    async def execute_script(self, script: str, page_type: Optional[str] = None) -> List[Dict]:
        """在页面中执行脚本"""
        results = []
        pages = self.get_pages(page_type)
        
        for page in pages:
            try:
                result = await page.evaluate(script)
                results.append({
                    'page_url': page.url,
                    'result': result
                })
            except Exception as e:
                logger.error(f"Failed to execute script on page {page.url}: {e}")
                results.append({
                    'page_url': page.url,
                    'error': str(e)
                })
        
        return results


    async def _try_existing_debug_port(self) -> bool:
        """策略1: 尝试连接现有的调试端口"""
        try:
            self._debug_port = ProcessUtils.get_steam_debug_port()
            if not self._debug_port:
                return False

            # 启动 Playwright
            self.playwright = await async_playwright().start()

            # 连接到 Chrome DevTools Protocol
            self.browser = await self.playwright.chromium.connect_over_cdp(
                f"http://localhost:{self._debug_port}"
            )

            # 获取默认上下文
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
                self.context.on("page", self._on_page_created)
                await self._discover_existing_pages()
                await self._setup_connection()
                return True

            return False

        except Exception as e:
            logger.debug(f"现有调试端口连接失败: {e}")
            return False

    async def _try_auto_start_steam_debug(self) -> bool:
        """策略2: 自动启动 Steam 调试模式"""
        try:
            # 查找 Steam 安装路径
            steam_exe = self._find_steam_executable()
            if not steam_exe:
                logger.debug("未找到 Steam 安装路径")
                return False

            # 检查 Steam 是否正在运行
            steam_proc = ProcessUtils.find_steam_process()
            if steam_proc:
                logger.info("检测到 Steam 正在运行，尝试重启以启用调试模式...")
                try:
                    # 优雅关闭 Steam
                    steam_proc.terminate()

                    # 等待进程结束
                    for i in range(10):  # 等待最多10秒
                        await asyncio.sleep(1)
                        if not steam_proc.is_running():
                            break

                    # 如果还在运行，强制关闭
                    if steam_proc.is_running():
                        logger.info("强制关闭 Steam 进程...")
                        steam_proc.kill()
                        await asyncio.sleep(3)

                except Exception as e:
                    logger.warning(f"关闭 Steam 进程时出错: {e}")

                # 确保所有 Steam 相关进程都已关闭
                await asyncio.sleep(2)

            # 启动带调试参数的 Steam
            debug_port = 9222
            cmd = [str(steam_exe), f"--remote-debugging-port={debug_port}"]

            logger.info(f"启动 Steam 调试模式: {' '.join(cmd)}")
            subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)

            # 等待 Steam 启动
            logger.info("等待 Steam 启动...")
            for i in range(60):  # 等待最多60秒
                await asyncio.sleep(1)

                # 检查端口是否可用
                if ProcessUtils.is_port_in_use(debug_port):
                    logger.info(f"Steam 调试端口 {debug_port} 已就绪")
                    self._debug_port = debug_port

                    # 等待额外2秒确保 Steam 完全启动
                    await asyncio.sleep(2)

                    # 尝试连接
                    if await self._try_existing_debug_port():
                        return True

                # 每10秒显示一次进度
                if i % 10 == 0 and i > 0:
                    logger.info(f"等待 Steam 启动... ({i}/60 秒)")

            logger.warning("Steam 调试模式启动超时，可能需要手动启动 Steam")
            return False

        except Exception as e:
            logger.debug(f"自动启动 Steam 调试模式失败: {e}")
            return False

    def _find_steam_executable(self) -> Optional[Path]:
        """查找 Steam 可执行文件"""
        for path in self.steam_paths:
            if path.exists():
                return path

        # 尝试从注册表查找 (Windows)
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\WOW6432Node\Valve\Steam")
            install_path = winreg.QueryValueEx(key, "InstallPath")[0]
            winreg.CloseKey(key)

            steam_exe = Path(install_path) / "Steam.exe"
            if steam_exe.exists():
                return steam_exe
        except:
            pass

        return None

    async def _try_standalone_browser(self) -> bool:
        """策略3: 启动独立浏览器模式"""
        try:
            logger.info("启动独立浏览器模式...")

            # 启动 Playwright
            self.playwright = await async_playwright().start()

            # 创建用户数据目录
            user_data_dir = Path.home() / '.steamplus' / 'browser_data'
            user_data_dir.mkdir(parents=True, exist_ok=True)

            # 使用 launch_persistent_context 而不是 launch
            self.context = await self.playwright.chromium.launch_persistent_context(
                user_data_dir=str(user_data_dir),
                headless=False,
                args=[
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled'
                ],
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )

            # 监听页面事件
            self.context.on("page", self._on_page_created)

            # 打开 Steam 商店页面
            page = await self.context.new_page()
            await page.goto("https://store.steampowered.com/", wait_until="domcontentloaded")

            await self._setup_connection()

            logger.info("独立浏览器模式启动成功")
            return True

        except Exception as e:
            logger.debug(f"独立浏览器模式失败: {e}")
            return False

    async def _enable_standalone_mode(self) -> bool:
        """策略4: 启用独立翻译模式"""
        try:
            logger.info("启用独立翻译模式（无需 Steam 连接）")

            # 模拟连接状态
            self._connected = True

            await self._setup_connection()

            # 发布连接事件
            await event_bus.publish(Events.STEAM_CONNECTED, {
                'mode': 'standalone',
                'message': '独立翻译模式已启用'
            })

            return True

        except Exception as e:
            logger.error(f"独立翻译模式启用失败: {e}")
            return False

    async def _setup_connection(self) -> None:
        """设置连接后的通用操作"""
        self._connected = True

        # 启动监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
        self._monitor_task = asyncio.create_task(self._monitor_connection())

        logger.info(f"Steam 连接设置完成，模式: {self._connection_mode}")


# 全局 Steam 连接实例
steam_connection = SteamConnection()
