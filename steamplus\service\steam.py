"""
Steam 连接管理模块

使用 Playwright-CDP 连接 Steam 客户端，实现页面注入和交互。
"""

import asyncio
import json
from typing import Dict, List, Optional, Set
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page
from loguru import logger

from .events import event_bus, Events
from .settings import get_settings
from .machine import ProcessUtils


class SteamConnection:
    """Steam 连接管理器"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.pages: Dict[str, Page] = {}
        self.playwright = None
        
        self._connected = False
        self._connecting = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._debug_port: Optional[int] = None
        
        # 支持的页面类型
        self.supported_pages = {
            'store': ['store.steampowered.com'],
            'community': ['steamcommunity.com'],
            'library': ['steamloopback.host'],
        }
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    async def connect(self) -> bool:
        """连接到 Steam"""
        if self._connected or self._connecting:
            return self._connected
        
        self._connecting = True
        
        try:
            # 查找 Steam 调试端口
            self._debug_port = ProcessUtils.get_steam_debug_port()

            if not self._debug_port:
                logger.warning("Steam debug port not found. Make sure Steam is running with remote debugging enabled.")
                logger.info("To enable Steam remote debugging, add '--remote-debugging-port=9222' to Steam launch options")
                return False
            
            # 启动 Playwright
            self.playwright = await async_playwright().start()
            
            # 连接到 Chrome DevTools Protocol
            try:
                self.browser = await self.playwright.chromium.connect_over_cdp(
                    f"http://localhost:{self._debug_port}"
                )
                logger.info(f"Connected to Steam on port {self._debug_port}")
            except Exception as e:
                logger.error(f"Failed to connect to Steam CDP: {e}")
                return False
            
            # 获取默认上下文
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                logger.error("No browser context found")
                return False
            
            # 监听页面事件
            self.context.on("page", self._on_page_created)
            
            # 获取现有页面
            await self._discover_existing_pages()
            
            self._connected = True
            
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._monitor_connection())
            
            # 发布连接事件
            await event_bus.publish(Events.STEAM_CONNECTED, {
                'debug_port': self._debug_port,
                'pages_count': len(self.pages)
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Steam: {e}")
            return False
        finally:
            self._connecting = False
    
    async def disconnect(self) -> None:
        """断开连接"""
        if not self._connected:
            return
        
        logger.info("Disconnecting from Steam...")
        
        self._connected = False
        
        # 停止监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        # 清理页面
        self.pages.clear()
        
        # 关闭浏览器连接
        if self.browser:
            try:
                await self.browser.close()
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
            self.browser = None
        
        # 停止 Playwright
        if self.playwright:
            try:
                await self.playwright.stop()
            except Exception as e:
                logger.warning(f"Error stopping playwright: {e}")
            self.playwright = None
        
        # 发布断开事件
        await event_bus.publish(Events.STEAM_DISCONNECTED)
        
        logger.info("Disconnected from Steam")
    
    async def _discover_existing_pages(self) -> None:
        """发现现有页面"""
        if not self.context:
            return
        
        try:
            pages = self.context.pages
            for page in pages:
                await self._handle_page(page)
        except Exception as e:
            logger.error(f"Failed to discover existing pages: {e}")
    
    async def _on_page_created(self, page: Page) -> None:
        """页面创建事件处理"""
        try:
            await self._handle_page(page)
        except Exception as e:
            logger.error(f"Error handling new page: {e}")
    
    async def _handle_page(self, page: Page) -> None:
        """处理页面"""
        try:
            url = page.url
            if not url:
                return
            
            # 检查是否是支持的页面类型
            page_type = self._get_page_type(url)
            if not page_type:
                return
            
            page_id = f"{page_type}_{id(page)}"
            self.pages[page_id] = page
            
            # 注入翻译脚本
            await self._inject_translation_script(page, page_type)
            
            # 监听页面关闭
            page.on("close", lambda: self._on_page_closed(page_id))
            
            logger.info(f"Handled {page_type} page: {url}")
            
            # 发布页面加载事件
            await event_bus.publish(Events.STEAM_PAGE_LOADED, {
                'page_id': page_id,
                'page_type': page_type,
                'url': url
            })
            
        except Exception as e:
            logger.error(f"Failed to handle page: {e}")
    
    def _get_page_type(self, url: str) -> Optional[str]:
        """获取页面类型"""
        for page_type, domains in self.supported_pages.items():
            for domain in domains:
                if domain in url:
                    return page_type
        return None
    
    def _on_page_closed(self, page_id: str) -> None:
        """页面关闭事件处理"""
        if page_id in self.pages:
            del self.pages[page_id]
            logger.debug(f"Page closed: {page_id}")
    
    async def _inject_translation_script(self, page: Page, page_type: str) -> None:
        """注入翻译脚本"""
        try:
            # 读取翻译脚本
            script_path = Path(__file__).parent.parent / "resources" / "inject.js"
            if not script_path.exists():
                logger.warning(f"Translation script not found: {script_path}")
                return
            
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            # 注入脚本
            await page.add_init_script(script_content)
            
            # 设置消息处理器
            await page.expose_function("steamplus_translate", self._handle_translation_request)
            
            logger.debug(f"Translation script injected into {page_type} page")
            
        except Exception as e:
            logger.error(f"Failed to inject translation script: {e}")
    
    async def _handle_translation_request(self, data: Dict) -> Dict:
        """处理翻译请求"""
        try:
            text = data.get('text', '')
            source_lang = data.get('source', 'auto')
            target_lang = data.get('target', 'zh-CN')

            if not text:
                return {'error': 'No text provided'}

            # 发布翻译开始事件
            await event_bus.publish(Events.TRANSLATION_STARTED, {
                'text': text,
                'source_lang': source_lang,
                'target_lang': target_lang
            })

            # 调用翻译服务
            from .translate import translation_service

            translation_result = await translation_service.translate(
                text=text,
                source_lang=source_lang,
                target_lang=target_lang
            )

            # 发布翻译完成事件
            await event_bus.publish(Events.TRANSLATION_COMPLETED, {
                'text': text,
                'result': translation_result.result,
                'provider': translation_result.provider,
                'duration': translation_result.duration
            })

            return {
                'result': translation_result.result,
                'source_lang': translation_result.source_lang,
                'target_lang': translation_result.target_lang,
                'provider': translation_result.provider,
                'confidence': translation_result.confidence,
                'cached': translation_result.cached,
                'duration': translation_result.duration
            }

        except Exception as e:
            logger.error(f"Translation request failed: {e}")
            await event_bus.publish(Events.TRANSLATION_FAILED, {
                'text': data.get('text', ''),
                'error': str(e)
            })
            return {'error': str(e)}
    
    async def _monitor_connection(self) -> None:
        """监控连接状态"""
        logger.info("Connection monitor started")
        
        while self._connected:
            try:
                # 检查浏览器连接状态
                if self.browser and not self.browser.is_connected():
                    logger.warning("Browser connection lost")
                    await self.disconnect()
                    break
                
                # 检查 Steam 进程状态
                if not ProcessUtils.find_steam_process():
                    logger.warning("Steam process not found")
                    await self.disconnect()
                    break
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection monitor: {e}")
                await asyncio.sleep(5)
        
        logger.info("Connection monitor stopped")
    
    def get_pages(self, page_type: Optional[str] = None) -> List[Page]:
        """获取页面列表"""
        if page_type:
            return [page for page_id, page in self.pages.items() 
                   if page_id.startswith(f"{page_type}_")]
        return list(self.pages.values())
    
    async def execute_script(self, script: str, page_type: Optional[str] = None) -> List[Dict]:
        """在页面中执行脚本"""
        results = []
        pages = self.get_pages(page_type)
        
        for page in pages:
            try:
                result = await page.evaluate(script)
                results.append({
                    'page_url': page.url,
                    'result': result
                })
            except Exception as e:
                logger.error(f"Failed to execute script on page {page.url}: {e}")
                results.append({
                    'page_url': page.url,
                    'error': str(e)
                })
        
        return results


# 全局 Steam 连接实例
steam_connection = SteamConnection()
