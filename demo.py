#!/usr/bin/env python3
"""
SteamPlus 演示脚本

展示 SteamPlus 的主要功能。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

from steamplus.service.events import event_bus, Events
from steamplus.service.settings import get_settings, save_settings
from steamplus.service.translate import translation_service
from steamplus.service.machine import machine_info


async def demo_events():
    """演示事件系统"""
    print("\n" + "="*50)
    print("事件系统演示")
    print("="*50)
    
    # 初始化事件总线
    await event_bus.initialize()
    
    events_received = []
    
    async def event_handler(event):
        events_received.append(f"{event.name}: {event.data}")
        print(f"📨 收到事件: {event.name}")
        if event.data:
            print(f"   数据: {event.data}")
    
    # 订阅事件
    event_bus.subscribe(Events.TRANSLATION_STARTED, event_handler)
    event_bus.subscribe(Events.TRANSLATION_COMPLETED, event_handler)
    event_bus.subscribe("demo_event", event_handler)
    
    # 发布测试事件
    await event_bus.publish("demo_event", {"message": "Hello from demo!"})
    await event_bus.publish(Events.TRANSLATION_STARTED, {"text": "Hello World"})
    await event_bus.publish(Events.TRANSLATION_COMPLETED, {
        "text": "Hello World", 
        "result": "你好世界"
    })
    
    # 等待事件处理
    await asyncio.sleep(0.2)
    
    print(f"✅ 处理了 {len(events_received)} 个事件")
    
    # 清理
    await event_bus.cleanup()


def demo_settings():
    """演示设置管理"""
    print("\n" + "="*50)
    print("设置管理演示")
    print("="*50)
    
    # 获取设置
    settings = get_settings()
    
    print("📋 当前设置:")
    print(f"   自动启动: {settings.general.autostart}")
    print(f"   更新通道: {settings.general.channel}")
    print(f"   翻译引擎: {settings.translate.provider}")
    print(f"   目标语言: {settings.translate.target_lang}")
    print(f"   自动连接Steam: {settings.steam.auto_connect}")
    
    # 修改设置
    original_provider = settings.translate.provider
    settings.translate.provider = "google"
    
    print(f"\n🔧 修改翻译引擎为: {settings.translate.provider}")
    
    # 保存设置
    save_settings(settings)
    print("💾 设置已保存")
    
    # 恢复原始设置
    settings.translate.provider = original_provider
    save_settings(settings)
    print(f"🔄 恢复翻译引擎为: {settings.translate.provider}")
    
    print("✅ 设置管理演示完成")


async def demo_translation():
    """演示翻译服务"""
    print("\n" + "="*50)
    print("翻译服务演示")
    print("="*50)
    
    # 初始化翻译服务
    await translation_service.initialize()
    
    # 获取可用的翻译提供商
    providers = translation_service.get_available_providers()
    print(f"🌐 可用的翻译提供商: {', '.join(providers)}")
    
    # 测试翻译（使用模拟数据，因为可能没有真实的API密钥）
    test_texts = [
        "Hello World",
        "Good morning",
        "How are you?",
        "Thank you very much"
    ]
    
    for text in test_texts:
        try:
            print(f"\n🔤 翻译: '{text}'")
            
            # 这里可能会失败，因为没有真实的API密钥
            # 但我们可以展示翻译服务的结构
            result = await translation_service.translate(text)
            
            print(f"   结果: '{result.result}'")
            print(f"   提供商: {result.provider}")
            print(f"   耗时: {result.duration:.2f}秒")
            print(f"   缓存: {'是' if result.cached else '否'}")
            
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
            print(f"   💡 这是正常的，因为没有配置真实的API密钥")
    
    # 测试缓存
    print(f"\n📦 缓存中有 {len(translation_service.cache)} 个翻译结果")
    
    # 清理
    await translation_service.cleanup()
    print("✅ 翻译服务演示完成")


def demo_machine_info():
    """演示设备信息"""
    print("\n" + "="*50)
    print("设备信息演示")
    print("="*50)
    
    print(f"🖥️  设备ID: {machine_info.machine_id}")
    
    system_info = machine_info.system_info
    print(f"💻 系统信息:")
    print(f"   操作系统: {system_info.get('system', 'Unknown')}")
    print(f"   版本: {system_info.get('release', 'Unknown')}")
    print(f"   架构: {system_info.get('machine', 'Unknown')}")
    print(f"   主机名: {system_info.get('hostname', 'Unknown')}")
    
    if 'memory_total' in system_info:
        memory_gb = system_info['memory_total'] / (1024**3)
        print(f"   内存: {memory_gb:.1f} GB")
    
    if 'cpu_count' in system_info:
        print(f"   CPU核心: {system_info['cpu_count']}")
    
    # 语言信息
    lang_info = machine_info.get_language_info()
    print(f"🌍 语言信息:")
    print(f"   默认语言: {lang_info.get('default_locale', 'Unknown')}")
    print(f"   编码: {lang_info.get('encoding', 'Unknown')}")
    
    # Steam语言检测
    steam_lang = machine_info.detect_steam_language()
    print(f"🎮 检测到的Steam语言: {steam_lang}")
    
    print("✅ 设备信息演示完成")


async def demo_api_server():
    """演示API服务器"""
    print("\n" + "="*50)
    print("API服务器演示")
    print("="*50)
    
    try:
        from steamplus.api.server import app
        from fastapi.testclient import TestClient
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试状态端点
        print("🔍 测试 /status 端点")
        response = client.get("/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   状态: {data.get('status')}")
            print(f"   版本: {data.get('version')}")
            print(f"   运行时间: {data.get('uptime', 0):.1f}秒")
            print("   ✅ 状态端点正常")
        else:
            print(f"   ❌ 状态端点失败: {response.status_code}")
        
        # 测试翻译提供商端点
        print("\n🌐 测试 /translate/providers 端点")
        response = client.get("/translate/providers")
        if response.status_code == 200:
            data = response.json()
            providers = data.get('providers', [])
            print(f"   可用提供商: {', '.join(providers)}")
            print("   ✅ 提供商端点正常")
        else:
            print(f"   ❌ 提供商端点失败: {response.status_code}")
        
        # 测试设置端点
        print("\n⚙️  测试 /settings 端点")
        response = client.get("/settings")
        if response.status_code == 200:
            data = response.json()
            print(f"   设置部分: {', '.join(data.keys())}")
            print("   ✅ 设置端点正常")
        else:
            print(f"   ❌ 设置端点失败: {response.status_code}")
        
        print("✅ API服务器演示完成")
        
    except Exception as e:
        print(f"❌ API服务器演示失败: {e}")


async def main():
    """主演示函数"""
    print("🚀 SteamPlus 功能演示")
    print("=" * 60)
    
    try:
        # 运行各个演示
        await demo_events()
        demo_settings()
        await demo_translation()
        demo_machine_info()
        await demo_api_server()
        
        print("\n" + "="*60)
        print("🎉 所有演示完成！")
        print("="*60)
        
        print("\n📝 总结:")
        print("✅ 事件系统 - 支持异步事件发布和订阅")
        print("✅ 设置管理 - 支持配置存储和验证")
        print("✅ 翻译服务 - 支持多引擎翻译（需要API密钥）")
        print("✅ 设备信息 - 支持系统信息收集")
        print("✅ API服务器 - 支持HTTP接口")
        
        print("\n🔧 下一步:")
        print("1. 配置翻译API密钥以启用真实翻译")
        print("2. 安装并启动Steam以测试注入功能")
        print("3. 运行完整应用: python -m steamplus.core start")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
