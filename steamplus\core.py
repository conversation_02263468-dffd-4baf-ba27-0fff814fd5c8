"""
SteamPlus 核心入口模块

提供命令行接口和应用程序启动逻辑。
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from loguru import logger

from .service.settings import get_settings
from .service.events import event_bus


class SteamPlusCore:
    """SteamPlus 核心应用类"""
    
    def __init__(self):
        self.running = False
        self.settings = None
        self.api_server = None
        self.steam_service = None
        self.tray_app = None
    
    async def initialize(self) -> None:
        """初始化应用程序"""
        logger.info("Initializing SteamPlus...")
        
        # 加载设置
        self.settings = get_settings()
        logger.info(f"Settings loaded: {self.settings.general.autostart}")
        
        # 初始化事件系统
        await event_bus.initialize()
        logger.info("Event bus initialized")
        
        # 初始化各个服务
        await self._initialize_services()
        
        logger.info("SteamPlus initialized successfully")

    async def _start_api_server(self) -> None:
        """启动 API 服务器"""
        try:
            import uvicorn
            from .api.server import app

            # 在后台启动 API 服务器
            config = uvicorn.Config(
                app=app,
                host="127.0.0.1",
                port=self.settings.general.api_port,
                log_level="info"
            )

            server = uvicorn.Server(config)

            # 在单独的任务中运行服务器
            self.api_server = asyncio.create_task(server.serve())
            logger.info(f"API server started on port {self.settings.general.api_port}")

        except Exception as e:
            logger.error(f"Failed to start API server: {e}")
            raise
    
    async def _initialize_services(self) -> None:
        """初始化各个服务"""
        try:
            # 初始化翻译服务
            from .service.translate import translation_service
            await translation_service.initialize()
            logger.info("Translation service initialized")

            # 如果设置了自动连接 Steam，则尝试连接
            if self.settings.steam.auto_connect:
                from .service.steam import steam_connection
                success = await steam_connection.connect()
                if success:
                    logger.info("Steam connection established")
                else:
                    logger.warning("Failed to connect to Steam")

            # 启动 API 服务器
            await self._start_api_server()

        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise
    
    async def start(self, with_ui: bool = True) -> None:
        """启动应用程序"""
        if self.running:
            logger.warning("SteamPlus is already running")
            return
        
        try:
            await self.initialize()
            
            self.running = True
            logger.info("SteamPlus started")
            
            if with_ui:
                await self._start_ui()
            else:
                # 无 UI 模式，保持运行
                await self._run_headless()
                
        except Exception as e:
            logger.error(f"Failed to start SteamPlus: {e}")
            await self.stop()
            raise
    
    async def _start_ui(self) -> None:
        """启动 UI 界面"""
        try:
            from .ui import tray_app
            
            # 在单独的线程中运行 Qt 应用
            import threading
            
            def run_qt():
                if tray_app.start():
                    tray_app.exec()
            
            ui_thread = threading.Thread(target=run_qt, daemon=True)
            ui_thread.start()
            
            logger.info("UI started")
            
            # 保持主线程运行
            while self.running:
                await asyncio.sleep(1)
                
        except ImportError:
            logger.warning("UI dependencies not available, running in headless mode")
            await self._run_headless()
    
    async def _run_headless(self) -> None:
        """无头模式运行"""
        logger.info("Running in headless mode")
        
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
            await self.stop()
    
    async def stop(self) -> None:
        """停止应用程序"""
        if not self.running:
            return
        
        logger.info("Stopping SteamPlus...")
        
        self.running = False
        
        # 停止各个服务
        if self.tray_app:
            self.tray_app.stop()

        # 停止 API 服务器
        if self.api_server:
            self.api_server.cancel()
            try:
                await self.api_server
            except asyncio.CancelledError:
                pass

        # 停止 Steam 连接
        try:
            from .service.steam import steam_connection
            await steam_connection.disconnect()
        except Exception as e:
            logger.warning(f"Error disconnecting Steam: {e}")

        # 清理翻译服务
        try:
            from .service.translate import translation_service
            await translation_service.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up translation service: {e}")

        # 清理事件系统
        await event_bus.cleanup()
        
        logger.info("SteamPlus stopped")


# 全局应用实例
app = SteamPlusCore()


@click.group()
@click.version_option(version="0.1.0", prog_name="SteamPlus")
def cli():
    """SteamPlus - Steam 即时翻译工具"""
    pass


@cli.command()
@click.option("--no-ui", is_flag=True, help="以无界面模式运行")
@click.option("--debug", is_flag=True, help="启用调试模式")
def start(no_ui: bool, debug: bool):
    """启动 SteamPlus"""
    if debug:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    try:
        asyncio.run(app.start(with_ui=not no_ui))
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


@cli.command()
def stop():
    """停止 SteamPlus"""
    asyncio.run(app.stop())


@cli.command()
def status():
    """查看 SteamPlus 状态"""
    # TODO: 实现状态查询
    click.echo("SteamPlus status: Not implemented yet")


def main():
    """主入口函数"""
    cli()


if __name__ == "__main__":
    main()
