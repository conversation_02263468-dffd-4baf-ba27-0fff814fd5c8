"""
翻译服务测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from steamplus.service.translate import (
    TranslationService, BingTranslator, GoogleTranslator,
    TranslationResult, TranslationProvider
)


class TestTranslationResult:
    """翻译结果测试"""
    
    def test_translation_result_creation(self):
        """测试翻译结果创建"""
        result = TranslationResult(
            text="Hello",
            result="你好",
            source_lang="en",
            target_lang="zh-CN",
            provider="bing"
        )
        
        assert result.text == "Hello"
        assert result.result == "你好"
        assert result.source_lang == "en"
        assert result.target_lang == "zh-CN"
        assert result.provider == "bing"
        assert result.confidence == 0.0
        assert result.cached is False
        assert result.duration == 0.0


class TestBingTranslator:
    """Bing 翻译器测试"""
    
    @pytest.mark.asyncio
    async def test_bing_translator_initialization(self):
        """测试 Bing 翻译器初始化"""
        translator = BingTranslator()
        
        assert translator.name == "bing"
        assert translator.enabled is True
        assert translator.session is None
        
        await translator.initialize()
        assert translator.session is not None
        
        await translator.cleanup()
        assert translator.session is None
    
    def test_bing_translator_availability(self):
        """测试 Bing 翻译器可用性"""
        translator = BingTranslator()
        
        # 没有 API 密钥时应该仍然可用（使用免费接口）
        assert translator.is_available() is True
        
        # 禁用时不可用
        translator.enabled = False
        assert translator.is_available() is False
    
    @pytest.mark.asyncio
    async def test_bing_translator_rate_limit(self):
        """测试速率限制"""
        translator = BingTranslator()
        translator.rate_limit = 1  # 每秒1个请求
        
        import time
        
        # 第一个请求应该立即通过
        start_time = time.time()
        await translator._rate_limit_check()
        first_duration = time.time() - start_time
        
        assert first_duration < 0.1  # 应该很快
        
        # 第二个请求应该被延迟
        start_time = time.time()
        await translator._rate_limit_check()
        second_duration = time.time() - start_time
        
        assert second_duration >= 0.9  # 应该等待约1秒


class TestGoogleTranslator:
    """Google 翻译器测试"""
    
    @pytest.mark.asyncio
    async def test_google_translator_initialization(self):
        """测试 Google 翻译器初始化"""
        translator = GoogleTranslator()
        
        assert translator.name == "google"
        assert translator.enabled is True
        
        await translator.initialize()
        assert translator.session is not None
        
        await translator.cleanup()
    
    def test_google_translator_availability(self):
        """测试 Google 翻译器可用性"""
        translator = GoogleTranslator()
        
        # 默认应该可用
        assert translator.is_available() is True
        
        # 禁用时不可用
        translator.enabled = False
        assert translator.is_available() is False


class TestTranslationService:
    """翻译服务测试"""
    
    @pytest.mark.asyncio
    async def test_translation_service_initialization(self):
        """测试翻译服务初始化"""
        service = TranslationService()
        
        assert "bing" in service.translators
        assert "google" in service.translators
        assert len(service.cache) == 0
        
        await service.initialize()
        
        # 清理
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_empty_text_translation(self):
        """测试空文本翻译"""
        service = TranslationService()
        
        result = await service.translate("")
        
        assert result.text == ""
        assert result.result == ""
        assert result.provider == "none"
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self):
        """测试缓存功能"""
        service = TranslationService()
        
        # 模拟翻译器
        mock_translator = AsyncMock()
        mock_translator.is_available.return_value = True
        mock_translator.translate.return_value = TranslationResult(
            text="Hello",
            result="你好",
            source_lang="en",
            target_lang="zh-CN",
            provider="mock"
        )
        
        service.translators["mock"] = mock_translator
        
        # 第一次翻译
        result1 = await service.translate("Hello", provider="mock")
        assert result1.result == "你好"
        assert result1.cached is False
        
        # 第二次翻译应该使用缓存
        result2 = await service.translate("Hello", provider="mock")
        assert result2.result == "你好"
        assert result2.cached is True
        
        # 翻译器应该只被调用一次
        assert mock_translator.translate.call_count == 1
    
    @pytest.mark.asyncio
    async def test_fallback_translation(self):
        """测试备用翻译器"""
        service = TranslationService()
        
        # 模拟主翻译器失败
        mock_primary = AsyncMock()
        mock_primary.is_available.return_value = True
        mock_primary.translate.side_effect = Exception("Primary failed")
        
        # 模拟备用翻译器成功
        mock_backup = AsyncMock()
        mock_backup.is_available.return_value = True
        mock_backup.translate.return_value = TranslationResult(
            text="Hello",
            result="你好",
            source_lang="en",
            target_lang="zh-CN",
            provider="backup"
        )
        
        service.translators = {
            "primary": mock_primary,
            "backup": mock_backup
        }
        
        # 翻译应该使用备用翻译器
        result = await service.translate("Hello", provider="primary")
        assert result.result == "你好"
        assert result.provider == "backup"
    
    @pytest.mark.asyncio
    async def test_no_available_translator(self):
        """测试没有可用翻译器的情况"""
        service = TranslationService()
        
        # 清空翻译器
        service.translators = {}
        
        # 应该返回原文
        result = await service.translate("Hello")
        assert result.text == "Hello"
        assert result.result == "Hello"
        assert result.provider == "fallback"
    
    def test_cache_size_limit(self):
        """测试缓存大小限制"""
        service = TranslationService()
        service.cache_size_limit = 2
        
        # 添加缓存项
        result1 = TranslationResult("text1", "result1", "en", "zh", "test")
        result2 = TranslationResult("text2", "result2", "en", "zh", "test")
        result3 = TranslationResult("text3", "result3", "en", "zh", "test")
        
        service._cache_result("key1", result1)
        service._cache_result("key2", result2)
        
        assert len(service.cache) == 2
        
        # 添加第三个应该删除第一个
        service._cache_result("key3", result3)
        
        assert len(service.cache) == 2
        assert "key1" not in service.cache
        assert "key2" in service.cache
        assert "key3" in service.cache
    
    def test_get_available_providers(self):
        """测试获取可用提供商"""
        service = TranslationService()
        
        # 模拟翻译器
        mock_available = MagicMock()
        mock_available.is_available.return_value = True
        
        mock_unavailable = MagicMock()
        mock_unavailable.is_available.return_value = False
        
        service.translators = {
            "available": mock_available,
            "unavailable": mock_unavailable
        }
        
        providers = service.get_available_providers()
        
        assert "available" in providers
        assert "unavailable" not in providers
    
    def test_clear_cache(self):
        """测试清空缓存"""
        service = TranslationService()
        
        # 添加一些缓存
        service.cache["key1"] = "value1"
        service.cache["key2"] = "value2"
        
        assert len(service.cache) == 2
        
        service.clear_cache()
        
        assert len(service.cache) == 0
