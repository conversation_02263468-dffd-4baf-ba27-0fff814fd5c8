#!/usr/bin/env python3
"""
Steam 连接诊断工具

帮助用户诊断和解决 Steam 连接问题。
"""

import sys
import asyncio
import subprocess
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))


async def diagnose_steam_connection():
    """诊断 Steam 连接"""
    print("🔍 Steam 连接诊断工具")
    print("=" * 50)
    
    try:
        from steamplus.service.machine import ProcessUtils
        
        # 1. 检查 Steam 进程
        print("1️⃣ 检查 Steam 进程...")
        steam_proc = ProcessUtils.find_steam_process()
        
        if steam_proc:
            print(f"   ✅ 找到 Steam 进程: PID {steam_proc.pid}")
            try:
                cmdline = steam_proc.cmdline()
                print(f"   📋 命令行: {' '.join(cmdline)}")
                
                # 检查是否有调试参数
                has_debug = any('--remote-debugging-port' in arg for arg in cmdline)
                if has_debug:
                    print("   ✅ Steam 已启用调试模式")
                else:
                    print("   ⚠️  Steam 未启用调试模式")
            except Exception as e:
                print(f"   ⚠️  无法获取命令行参数: {e}")
        else:
            print("   ❌ 未找到 Steam 进程")
            print("   💡 请先启动 Steam 客户端")
        
        # 2. 检查调试端口
        print("\n2️⃣ 检查调试端口...")
        common_ports = [9222, 9223, 9224, 9225, 9226]
        found_ports = []
        
        for port in common_ports:
            if ProcessUtils.is_port_in_use(port):
                found_ports.append(port)
                print(f"   ✅ 端口 {port} 正在使用")
            else:
                print(f"   ❌ 端口 {port} 未使用")
        
        if found_ports:
            print(f"   🎯 可用调试端口: {', '.join(map(str, found_ports))}")
        else:
            print("   ⚠️  未找到可用的调试端口")
        
        # 3. 检查 Steam 安装路径
        print("\n3️⃣ 检查 Steam 安装路径...")
        steam_paths = [
            Path("C:/Program Files (x86)/Steam/Steam.exe"),
            Path("C:/Program Files/Steam/Steam.exe"),
        ]
        
        found_steam = None
        for path in steam_paths:
            if path.exists():
                print(f"   ✅ 找到 Steam: {path}")
                found_steam = path
                break
            else:
                print(f"   ❌ 未找到: {path}")
        
        # 尝试从注册表查找
        if not found_steam:
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"SOFTWARE\WOW6432Node\Valve\Steam")
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                winreg.CloseKey(key)
                
                steam_exe = Path(install_path) / "Steam.exe"
                if steam_exe.exists():
                    print(f"   ✅ 从注册表找到 Steam: {steam_exe}")
                    found_steam = steam_exe
            except Exception as e:
                print(f"   ⚠️  注册表查找失败: {e}")
        
        if not found_steam:
            print("   ❌ 未找到 Steam 安装路径")
        
        # 4. 测试连接
        print("\n4️⃣ 测试连接...")
        if found_ports:
            for port in found_ports:
                success = await test_cdp_connection(port)
                if success:
                    print(f"   ✅ 端口 {port} 连接成功")
                else:
                    print(f"   ❌ 端口 {port} 连接失败")
        else:
            print("   ⚠️  没有可测试的端口")
        
        # 5. 提供解决方案
        print("\n5️⃣ 解决方案建议:")
        
        if not steam_proc:
            print("   🔧 启动 Steam 客户端")
        elif not found_ports:
            print("   🔧 重启 Steam 并添加调试参数:")
            print("      1. 关闭 Steam")
            print("      2. 右键 Steam 快捷方式 → 属性")
            print("      3. 在目标末尾添加: --remote-debugging-port=9222")
            print("      4. 重新启动 Steam")
            print()
            print("   🔧 或者使用自动重启功能:")
            print("      python start_stable.py")
        else:
            print("   ✅ Steam 连接配置正常")
            print("   💡 可以使用完整功能模式")
        
        # 6. 自动修复选项
        print("\n6️⃣ 自动修复选项:")
        if found_steam and steam_proc:
            choice = input("   是否尝试自动重启 Steam 并启用调试模式？(y/N): ")
            if choice.lower() == 'y':
                await auto_fix_steam(steam_proc, found_steam)
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()


async def test_cdp_connection(port):
    """测试 CDP 连接"""
    try:
        from playwright.async_api import async_playwright
        
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp(
            f"http://localhost:{port}"
        )
        
        # 测试基本功能
        contexts = browser.contexts
        if contexts:
            await browser.close()
            await playwright.stop()
            return True
        
        await browser.close()
        await playwright.stop()
        return False
        
    except Exception:
        return False


async def auto_fix_steam(steam_proc, steam_exe):
    """自动修复 Steam"""
    print("🔧 开始自动修复...")
    
    try:
        # 1. 关闭 Steam
        print("   ⏹️  关闭 Steam...")
        steam_proc.terminate()
        
        # 等待进程结束
        for i in range(10):
            await asyncio.sleep(1)
            if not steam_proc.is_running():
                break
        
        if steam_proc.is_running():
            print("   🔨 强制关闭 Steam...")
            steam_proc.kill()
            await asyncio.sleep(3)
        
        # 2. 启动带调试参数的 Steam
        print("   🚀 启动调试模式 Steam...")
        cmd = [str(steam_exe), "--remote-debugging-port=9222"]
        subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)
        
        # 3. 等待启动
        print("   ⏳ 等待 Steam 启动...")
        for i in range(30):
            await asyncio.sleep(1)
            
            from steamplus.service.machine import ProcessUtils
            if ProcessUtils.is_port_in_use(9222):
                print("   ✅ Steam 调试模式启动成功！")
                print("   🎉 现在可以使用完整的翻译功能了")
                return True
            
            if i % 5 == 0 and i > 0:
                print(f"   ⏳ 等待中... ({i}/30 秒)")
        
        print("   ⚠️  Steam 启动超时，请手动检查")
        return False
        
    except Exception as e:
        print(f"   ❌ 自动修复失败: {e}")
        return False


def main():
    """主函数"""
    try:
        asyncio.run(diagnose_steam_connection())
    except KeyboardInterrupt:
        print("\n⏹️  诊断被用户中断")
    except Exception as e:
        print(f"\n💥 诊断异常: {e}")


if __name__ == "__main__":
    main()
