"""
事件系统测试
"""

import asyncio
import pytest

from steamplus.service.events import EventBus, Event, Events


class TestEventBus:
    """事件总线测试类"""
    
    @pytest.mark.asyncio
    async def test_event_bus_initialization(self):
        """测试事件总线初始化"""
        bus = EventBus()
        
        assert not bus.is_running
        
        await bus.initialize()
        assert bus.is_running
        
        await bus.cleanup()
        assert not bus.is_running
    
    @pytest.mark.asyncio
    async def test_event_subscription(self):
        """测试事件订阅"""
        bus = EventBus()
        await bus.initialize()
        
        events_received = []
        
        async def handler(event):
            events_received.append(event.name)
        
        # 订阅事件
        bus.subscribe("test_event", handler)
        
        # 发布事件
        await bus.publish("test_event", "test_data")
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        assert "test_event" in events_received
        
        await bus.cleanup()
    
    @pytest.mark.asyncio
    async def test_multiple_subscribers(self):
        """测试多个订阅者"""
        bus = EventBus()
        await bus.initialize()
        
        events_received = []
        
        async def handler1(event):
            events_received.append(f"handler1_{event.name}")
        
        async def handler2(event):
            events_received.append(f"handler2_{event.name}")
        
        # 订阅同一事件
        bus.subscribe("test_event", handler1)
        bus.subscribe("test_event", handler2)
        
        # 发布事件
        await bus.publish("test_event", "test_data")
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        assert "handler1_test_event" in events_received
        assert "handler2_test_event" in events_received
        
        await bus.cleanup()
    
    @pytest.mark.asyncio
    async def test_event_unsubscription(self):
        """测试取消订阅"""
        bus = EventBus()
        await bus.initialize()
        
        events_received = []
        
        async def handler(event):
            events_received.append(event.name)
        
        # 订阅事件
        bus.subscribe("test_event", handler)
        
        # 发布事件
        await bus.publish("test_event", "test_data")
        await asyncio.sleep(0.1)
        
        assert len(events_received) == 1
        
        # 取消订阅
        bus.unsubscribe("test_event", handler)
        
        # 再次发布事件
        await bus.publish("test_event", "test_data")
        await asyncio.sleep(0.1)
        
        # 事件数量应该没有增加
        assert len(events_received) == 1
        
        await bus.cleanup()
    
    @pytest.mark.asyncio
    async def test_event_data(self):
        """测试事件数据传递"""
        bus = EventBus()
        await bus.initialize()
        
        received_data = []
        
        async def handler(event):
            received_data.append(event.data)
        
        bus.subscribe("test_event", handler)
        
        test_data = {"key": "value", "number": 42}
        await bus.publish("test_event", test_data)
        
        await asyncio.sleep(0.1)
        
        assert len(received_data) == 1
        assert received_data[0] == test_data
        
        await bus.cleanup()
    
    @pytest.mark.asyncio
    async def test_sync_publish(self):
        """测试同步发布"""
        bus = EventBus()
        await bus.initialize()
        
        events_received = []
        
        def sync_handler(event):
            events_received.append(event.name)
        
        bus.subscribe("test_event", sync_handler)
        
        # 使用同步发布
        bus.publish_sync("test_event", "test_data")
        
        await asyncio.sleep(0.1)
        
        assert "test_event" in events_received
        
        await bus.cleanup()
    
    def test_event_constants(self):
        """测试事件常量"""
        # 确保所有事件常量都是字符串
        assert isinstance(Events.STEAM_CONNECTED, str)
        assert isinstance(Events.STEAM_DISCONNECTED, str)
        assert isinstance(Events.TRANSLATION_STARTED, str)
        assert isinstance(Events.TRANSLATION_COMPLETED, str)
        assert isinstance(Events.SETTINGS_CHANGED, str)
        
        # 确保事件名称不为空
        assert Events.STEAM_CONNECTED
        assert Events.STEAM_DISCONNECTED
        assert Events.TRANSLATION_STARTED
        assert Events.TRANSLATION_COMPLETED
        assert Events.SETTINGS_CHANGED


class TestEvent:
    """事件数据类测试"""
    
    def test_event_creation(self):
        """测试事件创建"""
        event = Event(name="test_event", data="test_data")
        
        assert event.name == "test_event"
        assert event.data == "test_data"
        assert event.source is None
        assert event.timestamp is not None
    
    def test_event_with_source(self):
        """测试带来源的事件"""
        event = Event(name="test_event", data="test_data", source="test_source")
        
        assert event.name == "test_event"
        assert event.data == "test_data"
        assert event.source == "test_source"
    
    def test_event_timestamp(self):
        """测试事件时间戳"""
        import time
        
        before = time.time()
        event = Event(name="test_event")
        after = time.time()
        
        assert before <= event.timestamp <= after
