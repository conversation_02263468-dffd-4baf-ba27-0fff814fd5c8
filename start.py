#!/usr/bin/env python3
"""
SteamPlus 启动脚本

用于启动 SteamPlus 应用程序的便捷脚本。
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查必要的依赖"""
    required_modules = [
        'loguru', 'click', 'pydantic', 'fastapi', 'uvicorn',
        'aiohttp', 'psutil', 'keyring'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
        except ImportError:
            missing.append(module)
    
    if missing:
        print("❌ 缺少必要的依赖:")
        for module in missing:
            print(f"   - {module}")
        print("\n请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    return True


def check_optional_dependencies():
    """检查可选依赖"""
    optional_modules = {
        'PySide6': '系统托盘界面',
        'playwright': 'Steam 页面注入',
    }
    
    available = []
    missing = []
    
    for module, description in optional_modules.items():
        try:
            __import__(module.replace('-', '_'))
            available.append((module, description))
        except ImportError:
            missing.append((module, description))
    
    if available:
        print("✅ 可用的可选功能:")
        for module, desc in available:
            print(f"   - {desc} ({module})")
    
    if missing:
        print("\n⚠️  不可用的可选功能:")
        for module, desc in missing:
            print(f"   - {desc} (需要 {module})")
        print("\n安装可选依赖:")
        for module, _ in missing:
            print(f"pip install {module}")
    
    return len(available) > 0


async def start_headless():
    """启动无界面模式"""
    print("🚀 启动 SteamPlus (无界面模式)")
    
    try:
        from steamplus.core import app
        await app.start(with_ui=False)
    except KeyboardInterrupt:
        print("\n⏹️  收到中断信号，正在停止...")
        await app.stop()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()


async def start_with_ui():
    """启动带界面模式"""
    print("🚀 启动 SteamPlus (带界面模式)")
    
    try:
        from steamplus.core import app
        await app.start(with_ui=True)
    except KeyboardInterrupt:
        print("\n⏹️  收到中断信号，正在停止...")
        await app.stop()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()


def start_api_only():
    """仅启动 API 服务器"""
    print("🌐 启动 SteamPlus API 服务器")
    
    try:
        import uvicorn
        from steamplus.api.server import app
        from steamplus.service.settings import get_settings
        
        settings = get_settings()
        port = settings.general.api_port
        
        print(f"📡 API 服务器将在 http://localhost:{port} 启动")
        print("📖 API 文档: http://localhost:{port}")
        print("🔄 按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=port,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n⏹️  API 服务器已停止")
    except Exception as e:
        print(f"❌ API 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()


def show_help():
    """显示帮助信息"""
    print("""
🎮 SteamPlus - Steam 即时翻译工具

用法:
    python start.py [选项]

选项:
    --headless, -h    无界面模式启动
    --ui, -u          带界面模式启动 (默认)
    --api-only, -a    仅启动 API 服务器
    --demo, -d        运行功能演示
    --help            显示此帮助信息

功能特性:
    🚀 即时翻译 - 支持 Steam 页面划词翻译
    🔄 多引擎切换 - 支持 Bing、DeepL、Google 等
    🎯 浏览器级注入 - 基于 Playwright-CDP
    🖥️ 系统托盘 - 简洁的托盘界面
    🔧 插件化架构 - 支持功能扩展
    📡 本地 API - 提供 HTTP 接口

配置文件位置: ~/.steamplus/
API 文档: http://localhost:50055 (启动后访问)
""")


def main():
    """主函数"""
    args = sys.argv[1:]
    
    # 解析命令行参数
    if '--help' in args or len(args) == 0:
        show_help()
        return
    
    if '--demo' in args or '-d' in args:
        print("🎬 运行功能演示...")
        os.system("python demo.py")
        return
    
    # 检查依赖
    print("🔍 检查依赖...")
    if not check_dependencies():
        return
    
    print("\n🔍 检查可选依赖...")
    has_optional = check_optional_dependencies()
    
    print("\n" + "="*50)
    
    # 根据参数启动不同模式
    if '--headless' in args or '-h' in args:
        asyncio.run(start_headless())
    elif '--api-only' in args or '-a' in args:
        start_api_only()
    elif '--ui' in args or '-u' in args:
        if 'PySide6' not in sys.modules:
            try:
                import PySide6
            except ImportError:
                print("❌ 界面模式需要 PySide6")
                print("安装命令: pip install PySide6")
                return
        asyncio.run(start_with_ui())
    else:
        # 默认尝试带界面模式，失败则使用无界面模式
        try:
            import PySide6
            asyncio.run(start_with_ui())
        except ImportError:
            print("⚠️  PySide6 不可用，使用无界面模式")
            asyncio.run(start_headless())


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        import traceback
        traceback.print_exc()
