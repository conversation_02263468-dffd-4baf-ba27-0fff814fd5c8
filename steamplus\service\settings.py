"""
设置管理模块

使用 Pydantic 进行配置验证，SQLite 存储配置数据，keyring 存储敏感信息。
"""

import sqlite3
from pathlib import Path
from typing import Any, Dict, Optional
import json

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
import keyring
from loguru import logger

from .events import event_bus, Events


class GeneralSettings(BaseModel):
    """通用设置"""
    autostart: bool = Field(default=True, description="开机自启动")
    channel: str = Field(default="stable", description="更新通道")
    language: str = Field(default="zh-CN", description="界面语言")
    log_level: str = Field(default="INFO", description="日志级别")
    api_port: int = Field(default=50055, description="API 服务端口")


class TranslateSettings(BaseModel):
    """翻译设置"""
    provider: str = Field(default="bing", description="翻译引擎")
    auto_detect: bool = Field(default=True, description="自动检测语言")
    source_lang: str = Field(default="auto", description="源语言")
    target_lang: str = Field(default="zh-CN", description="目标语言")
    show_original: bool = Field(default=True, description="显示原文")
    translation_delay: float = Field(default=0.5, description="翻译延迟（秒）")


class SteamSettings(BaseModel):
    """Steam 设置"""
    auto_connect: bool = Field(default=True, description="自动连接 Steam")
    inject_pages: list[str] = Field(
        default_factory=lambda: ["store", "community", "library"],
        description="注入页面类型"
    )
    debug_port: Optional[int] = Field(default=None, description="调试端口")


class UISettings(BaseModel):
    """界面设置"""
    show_notifications: bool = Field(default=True, description="显示通知")
    minimize_to_tray: bool = Field(default=True, description="最小化到托盘")
    close_to_tray: bool = Field(default=True, description="关闭到托盘")
    theme: str = Field(default="system", description="主题")


class Settings(BaseModel):
    """主设置类"""
    general: GeneralSettings = Field(default_factory=GeneralSettings)
    translate: TranslateSettings = Field(default_factory=TranslateSettings)
    steam: SteamSettings = Field(default_factory=SteamSettings)
    ui: UISettings = Field(default_factory=UISettings)


class SettingsManager:
    """设置管理器"""
    
    def __init__(self):
        self.app_name = "SteamPlus"
        self.config_dir = Path.home() / ".steamplus"
        self.config_dir.mkdir(exist_ok=True)
        
        self.db_path = self.config_dir / "settings.db"
        self._settings: Optional[Settings] = None
        
        self._init_database()
    
    def _init_database(self) -> None:
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
    
    def load(self) -> Settings:
        """加载设置"""
        if self._settings is not None:
            return self._settings
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT key, value FROM settings")
                data = {}
                
                for key, value in cursor.fetchall():
                    try:
                        data[key] = json.loads(value)
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse setting: {key}")
                
                # 构建嵌套字典结构
                nested_data = {}
                for key, value in data.items():
                    parts = key.split('.')
                    current = nested_data
                    
                    for part in parts[:-1]:
                        if part not in current:
                            current[part] = {}
                        current = current[part]
                    
                    current[parts[-1]] = value
                
                self._settings = Settings(**nested_data)
                
        except Exception as e:
            logger.warning(f"Failed to load settings: {e}, using defaults")
            self._settings = Settings()
        
        # 发布设置加载事件
        event_bus.publish_sync(Events.SETTINGS_LOADED, self._settings)
        
        return self._settings
    
    def save(self, settings: Settings) -> None:
        """保存设置"""
        try:
            # 将设置转换为扁平化的键值对
            flat_data = self._flatten_dict(settings.model_dump())
            
            with sqlite3.connect(self.db_path) as conn:
                for key, value in flat_data.items():
                    conn.execute(
                        "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                        (key, json.dumps(value))
                    )
                conn.commit()
            
            self._settings = settings
            
            # 发布设置变更事件
            event_bus.publish_sync(Events.SETTINGS_CHANGED, settings)
            
            logger.info("Settings saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
            raise
    
    def _flatten_dict(self, data: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """将嵌套字典扁平化"""
        result = {}
        
        for key, value in data.items():
            new_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                result.update(self._flatten_dict(value, new_key))
            else:
                result[new_key] = value
        
        return result
    
    def get_secure(self, key: str) -> Optional[str]:
        """从 keyring 获取安全存储的值"""
        try:
            return keyring.get_password(self.app_name, key)
        except Exception as e:
            logger.error(f"Failed to get secure value for {key}: {e}")
            return None
    
    def set_secure(self, key: str, value: str) -> None:
        """将值安全存储到 keyring"""
        try:
            keyring.set_password(self.app_name, key, value)
            logger.debug(f"Secure value set for {key}")
        except Exception as e:
            logger.error(f"Failed to set secure value for {key}: {e}")
            raise
    
    def delete_secure(self, key: str) -> None:
        """从 keyring 删除安全存储的值"""
        try:
            keyring.delete_password(self.app_name, key)
            logger.debug(f"Secure value deleted for {key}")
        except Exception as e:
            logger.error(f"Failed to delete secure value for {key}: {e}")
    
    def reset(self) -> Settings:
        """重置为默认设置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM settings")
                conn.commit()
            
            self._settings = Settings()
            self.save(self._settings)
            
            logger.info("Settings reset to defaults")
            return self._settings
            
        except Exception as e:
            logger.error(f"Failed to reset settings: {e}")
            raise


# 全局设置管理器实例
_settings_manager = SettingsManager()


def get_settings() -> Settings:
    """获取设置实例"""
    return _settings_manager.load()


def save_settings(settings: Settings) -> None:
    """保存设置"""
    _settings_manager.save(settings)


def get_secure_setting(key: str) -> Optional[str]:
    """获取安全设置"""
    return _settings_manager.get_secure(key)


def set_secure_setting(key: str, value: str) -> None:
    """设置安全设置"""
    _settings_manager.set_secure(key, value)


def delete_secure_setting(key: str) -> None:
    """删除安全设置"""
    _settings_manager.delete_secure(key)
