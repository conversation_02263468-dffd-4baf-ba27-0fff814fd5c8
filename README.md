# SteamPlus

Steam 即时翻译工具 - Python 重构版

## 🎮 功能特性

- 🚀 **即时翻译**: 支持 Steam 页面划词翻译
- 🔄 **多引擎切换**: 支持 Bing、DeepL、Google 等翻译引擎
- 🎯 **浏览器级注入**: 基于 Playwright-CDP 的稳定注入机制
- 🖥️ **系统托盘**: 简洁的系统托盘界面
- 🔧 **插件化架构**: 支持第三方功能扩展
- 📡 **本地 API**: 提供 FastAPI 本地服务接口
- 🔄 **自动更新**: 支持增量更新和版本管理

## 📋 系统要求

- Windows 10/11 (x64)
- Python 3.12+
- Steam 客户端

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装核心依赖
pip install loguru click pydantic pydantic-settings fastapi uvicorn aiohttp psutil keyring

# 安装可选依赖（推荐）
pip install PySide6 playwright packaging

# 如果安装了 playwright，需要安装浏览器
playwright install chromium
```

### 2. 运行应用

```bash
# 方式1: 使用启动脚本（推荐）
python start.py

# 方式2: 仅启动 API 服务器
python start.py --api-only

# 方式3: 无界面模式
python start.py --headless

# 方式4: 运行功能演示
python start.py --demo
```

### 3. 验证安装

```bash
# 运行功能演示
python demo.py

# 查看帮助
python start.py --help
```

## 🏗️ 架构设计

```
┌────────────────────┐
│    steamplus.ui    │   ← PySide6 系统托盘 (可选)
└────────┬───────────┘
         │
┌────────▼───────────┐
│  steamplus.service │   ← 核心业务逻辑
│  ├─ steam.py       │   Steam 会话管理 / CDP 注入
│  ├─ translate.py   │   统一翻译适配层
│  ├─ settings.py    │   配置管理
│  ├─ updater.py     │   自动更新
│  ├─ machine.py     │   设备信息
│  └─ events.py      │   事件总线
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.api      │   ← FastAPI 本地服务
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.plugins  │   ← 插件系统
└────────────────────┘
```

## ⚙️ 配置

配置文件位于用户目录下的 `.steamplus/` 文件夹中：

- `settings.db`: SQLite 数据库存储配置
- `logs/`: 日志文件目录

敏感信息（如 API 密钥）使用系统 keyring 安全存储。

### 翻译引擎配置

1. **Bing 翻译** (默认)
   - 免费额度：无需配置
   - 官方 API：需要在设置中配置 API 密钥

2. **Google 翻译**
   - 使用免费接口，无需配置

3. **DeepL 翻译**
   - 需要 DeepL API 密钥

## 📡 API 文档

启动服务后访问 http://localhost:50055 查看完整的 API 文档。

### 主要接口

- `GET /status`: 获取服务状态
- `POST /translate`: 翻译文本
- `GET /translate/providers`: 获取可用翻译提供商
- `GET /settings`: 获取配置
- `PUT /settings`: 更新配置
- `GET /steam/status`: 获取 Steam 连接状态
- `POST /steam/connect`: 连接到 Steam
- `GET /system/info`: 获取系统信息

### API 使用示例

```bash
# 获取状态
curl http://localhost:50055/status

# 翻译文本
curl -X POST http://localhost:50055/translate \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello World", "target_lang": "zh-CN"}'

# 获取可用翻译提供商
curl http://localhost:50055/translate/providers
```

## 🎯 使用说明

### Steam 连接

1. **启动 Steam 客户端**
   - 确保 Steam 客户端正在运行
   - SteamPlus 会自动检测并连接到 Steam

2. **启用远程调试** (如果自动连接失败)
   ```bash
   # 在 Steam 启动参数中添加
   --remote-debugging-port=9222
   ```

3. **验证连接**
   - 系统托盘图标会显示连接状态
   - 访问 http://localhost:50055/steam/status 查看连接状态

### 翻译功能

1. **划词翻译**
   - 在 Steam 页面中选中文本
   - 等待 0.5 秒后会显示翻译结果

2. **快捷键**
   - `Ctrl+Shift+T`: 切换翻译功能开关
   - `ESC`: 隐藏翻译提示框

3. **支持的页面**
   - Steam 商店页面
   - Steam 社区页面
   - Steam 库页面

## 🔧 插件开发

```python
from steamplus.plugins import BasePlugin

class MyPlugin(BasePlugin):
    name = "my_plugin"
    version = "1.0.0"
    description = "我的自定义插件"

    async def initialize(self):
        # 插件初始化逻辑
        print(f"插件 {self.name} 已初始化")

    async def on_translation(self, text: str, result: str):
        # 翻译完成时的回调
        print(f"翻译完成: {text} -> {result}")

    async def on_steam_connected(self):
        # Steam 连接时的回调
        print("Steam 已连接")
```

### 插件安装

1. 将插件文件放在 `steamplus/plugins/` 目录下
2. 在 `pyproject.toml` 中注册插件入口点
3. 重启 SteamPlus

## 🐛 故障排除

### 常见问题

1. **无法连接到 Steam**
   ```
   解决方案:
   - 确保 Steam 客户端正在运行
   - 检查 Steam 是否启用了远程调试
   - 尝试手动指定调试端口
   ```

2. **翻译功能不工作**
   ```
   解决方案:
   - 检查翻译引擎是否可用
   - 验证 API 密钥配置
   - 查看日志文件中的错误信息
   ```

3. **系统托盘图标不显示**
   ```
   解决方案:
   - 确保已安装 PySide6
   - 检查系统托盘设置
   - 尝试使用无界面模式
   ```

### 日志文件

日志文件位置: `~/.steamplus/logs/`

查看实时日志:
```bash
# Windows
tail -f %USERPROFILE%\.steamplus\logs\steamplus.log

# 或者在应用中查看
python start.py --debug
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- 感谢 [SteamMeta](https://github.com/steamplus/steammeta) 项目提供的设计灵感
- 感谢所有贡献者和用户的支持
