# SteamPlus

Steam 即时翻译工具 - Python 重构版

## 功能特性

- 🚀 **即时翻译**: 支持 Steam 页面划词翻译
- 🔄 **多引擎切换**: 支持 Bing、DeepL、Google 等翻译引擎
- 🎯 **浏览器级注入**: 基于 Playwright-CDP 的稳定注入机制
- 🖥️ **系统托盘**: 简洁的系统托盘界面
- 🔧 **插件化架构**: 支持第三方功能扩展
- 📡 **本地 API**: 提供 FastAPI 本地服务接口
- 🔄 **自动更新**: 支持增量更新和版本管理

## 系统要求

- Windows 10/11 (x64)
- Python 3.12+
- Steam 客户端

## 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/steamplus/steamplus.git
cd steamplus

# 安装依赖
pip install -e .[dev]

# 安装 Playwright 浏览器
playwright install chromium
```

### 运行

```bash
# 启动 SteamPlus
steamplus

# 或者直接运行
python -m steamplus.core
```

### 开发模式

```bash
# 启动开发服务器
uvicorn steamplus.api.server:app --reload --port 50055

# 运行测试
pytest

# 代码格式化
black steamplus/
isort steamplus/
```

## 架构设计

```
┌────────────────────┐
│    steamplus.ui    │   ← PySide6 系统托盘 (可选)
└────────┬───────────┘
         │
┌────────▼───────────┐
│  steamplus.service │   ← 核心业务逻辑
│  ├─ steam.py       │   Steam 会话管理 / CDP 注入
│  ├─ translate.py   │   统一翻译适配层
│  ├─ settings.py    │   配置管理
│  ├─ updater.py     │   自动更新
│  ├─ machine.py     │   设备信息
│  └─ events.py      │   事件总线
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.api      │   ← FastAPI 本地服务
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.plugins  │   ← 插件系统
└────────────────────┘
```

## 配置

配置文件位于用户目录下的 `.steamplus/` 文件夹中：

- `settings.db`: SQLite 数据库存储配置
- `logs/`: 日志文件目录

敏感信息（如 API 密钥）使用系统 keyring 安全存储。

## API 文档

启动服务后访问 http://localhost:50055 查看完整的 API 文档。

主要接口：
- `GET /status`: 获取服务状态
- `POST /translate`: 翻译文本
- `GET /settings`: 获取配置
- `PUT /settings`: 更新配置

## 插件开发

```python
from steamplus.plugins import BasePlugin

class MyPlugin(BasePlugin):
    name = "my_plugin"
    version = "1.0.0"
    
    async def initialize(self):
        # 插件初始化逻辑
        pass
    
    async def on_translation(self, text: str, result: str):
        # 翻译完成时的回调
        pass
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- 感谢 [SteamMeta](https://github.com/steamplus/steammeta) 项目提供的设计灵感
- 感谢所有贡献者和用户的支持
