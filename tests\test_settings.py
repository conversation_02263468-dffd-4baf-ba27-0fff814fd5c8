"""
设置管理测试
"""

import pytest
import tempfile
from pathlib import Path

from steamplus.service.settings import Settings, SettingsManager


class TestSettings:
    """设置测试类"""
    
    def test_default_settings(self):
        """测试默认设置"""
        settings = Settings()
        
        assert settings.general.autostart is True
        assert settings.general.channel == "stable"
        assert settings.translate.provider == "bing"
        assert settings.translate.auto_detect is True
        assert settings.steam.auto_connect is True
        assert settings.ui.show_notifications is True
    
    def test_settings_validation(self):
        """测试设置验证"""
        # 测试有效设置
        settings = Settings(
            general={'autostart': False, 'channel': 'beta'},
            translate={'provider': 'google', 'target_lang': 'en'}
        )
        
        assert settings.general.autostart is False
        assert settings.general.channel == "beta"
        assert settings.translate.provider == "google"
        assert settings.translate.target_lang == "en"
    
    def test_settings_manager(self):
        """测试设置管理器"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时设置管理器
            manager = SettingsManager()
            manager.config_dir = Path(temp_dir)
            manager.db_path = manager.config_dir / "test_settings.db"
            manager._init_database()
            
            # 测试加载默认设置
            settings = manager.load()
            assert isinstance(settings, Settings)
            assert settings.general.autostart is True
            
            # 测试保存设置
            settings.general.autostart = False
            settings.translate.provider = "google"
            manager.save(settings)
            
            # 测试重新加载
            manager._settings = None  # 清除缓存
            loaded_settings = manager.load()
            assert loaded_settings.general.autostart is False
            assert loaded_settings.translate.provider == "google"
    
    def test_secure_settings(self):
        """测试安全设置存储"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = SettingsManager()
            manager.config_dir = Path(temp_dir)
            
            # 注意：这个测试可能在某些环境中失败，因为需要系统 keyring 支持
            try:
                # 测试设置安全值
                manager.set_secure("test_key", "test_value")
                
                # 测试获取安全值
                value = manager.get_secure("test_key")
                assert value == "test_value"
                
                # 测试删除安全值
                manager.delete_secure("test_key")
                value = manager.get_secure("test_key")
                assert value is None
                
            except Exception:
                # 如果 keyring 不可用，跳过测试
                pytest.skip("Keyring not available in test environment")


@pytest.mark.asyncio
async def test_settings_events():
    """测试设置事件"""
    import asyncio
    from steamplus.service.events import event_bus, Events

    # 初始化事件总线
    await event_bus.initialize()

    events_received = []

    async def event_handler(event):
        events_received.append(event.name)

    # 订阅设置变更事件
    event_bus.subscribe(Events.SETTINGS_CHANGED, event_handler)

    # 创建临时设置管理器
    with tempfile.TemporaryDirectory() as temp_dir:
        manager = SettingsManager()
        manager.config_dir = Path(temp_dir)
        manager.db_path = manager.config_dir / "test_settings.db"
        manager._init_database()

        # 保存设置应该触发事件
        settings = Settings()
        manager.save(settings)

        # 等待事件处理
        await asyncio.sleep(0.1)

        assert Events.SETTINGS_CHANGED in events_received

    # 清理
    await event_bus.cleanup()
