"""
集成测试

测试各个模块之间的协作。
"""

import asyncio
import pytest
from unittest.mock import patch, AsyncMock

from steamplus.core import SteamPlusCore
from steamplus.service.events import event_bus
from steamplus.service.settings import get_settings


class TestIntegration:
    """集成测试类"""
    
    @pytest.mark.asyncio
    async def test_core_initialization(self):
        """测试核心应用初始化"""
        core = SteamPlusCore()
        
        # 模拟各个服务的初始化
        with patch('steamplus.service.translate.translation_service.initialize') as mock_translate_init, \
             patch('steamplus.service.steam.steam_connection.connect') as mock_steam_connect, \
             patch.object(core, '_start_api_server') as mock_api_start:
            
            mock_translate_init.return_value = None
            mock_steam_connect.return_value = True
            mock_api_start.return_value = None
            
            await core.initialize()
            
            assert core.settings is not None
            mock_translate_init.assert_called_once()
            mock_api_start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_event_flow(self):
        """测试事件流"""
        await event_bus.initialize()
        
        events_received = []
        
        async def event_handler(event):
            events_received.append(event.name)
        
        # 订阅各种事件
        event_bus.subscribe("steam_connected", event_handler)
        event_bus.subscribe("translation_completed", event_handler)
        event_bus.subscribe("settings_changed", event_handler)
        
        # 模拟事件发布
        await event_bus.publish("steam_connected", {"port": 9222})
        await event_bus.publish("translation_completed", {"text": "Hello", "result": "你好"})
        await event_bus.publish("settings_changed", {"section": "translate"})
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        assert "steam_connected" in events_received
        assert "translation_completed" in events_received
        assert "settings_changed" in events_received
        
        await event_bus.cleanup()
    
    @pytest.mark.asyncio
    async def test_translation_workflow(self):
        """测试翻译工作流"""
        from steamplus.service.translate import translation_service
        
        # 初始化翻译服务
        await translation_service.initialize()
        
        # 模拟翻译器
        mock_translator = AsyncMock()
        mock_translator.is_available.return_value = True
        mock_translator.translate.return_value = type('TranslationResult', (), {
            'text': 'Hello',
            'result': '你好',
            'source_lang': 'en',
            'target_lang': 'zh-CN',
            'provider': 'mock',
            'confidence': 0.9,
            'cached': False,
            'duration': 0.1
        })()
        
        translation_service.translators['mock'] = mock_translator
        
        # 执行翻译
        result = await translation_service.translate("Hello", provider="mock")
        
        assert result.text == "Hello"
        assert result.result == "你好"
        assert result.provider == "mock"
        
        # 清理
        await translation_service.cleanup()
    
    @pytest.mark.asyncio
    async def test_settings_integration(self):
        """测试设置集成"""
        # 获取设置
        settings = get_settings()
        
        # 验证默认设置
        assert settings.general.autostart is True
        assert settings.translate.provider == "bing"
        assert settings.steam.auto_connect is True
        
        # 修改设置
        original_provider = settings.translate.provider
        settings.translate.provider = "google"
        
        # 验证修改
        assert settings.translate.provider == "google"
        
        # 恢复原始设置
        settings.translate.provider = original_provider
    
    @pytest.mark.asyncio
    async def test_api_endpoints(self):
        """测试 API 端点"""
        from steamplus.api.server import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # 测试状态端点
        response = client.get("/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "version" in data
        assert data["status"] == "running"
        
        # 测试翻译提供商端点
        response = client.get("/translate/providers")
        assert response.status_code == 200
        
        data = response.json()
        assert "providers" in data
        assert isinstance(data["providers"], list)
        
        # 测试设置端点
        response = client.get("/settings")
        assert response.status_code == 200
        
        data = response.json()
        assert "general" in data
        assert "translate" in data
        assert "steam" in data
        assert "ui" in data
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        from steamplus.service.translate import translation_service
        
        await translation_service.initialize()
        
        # 模拟失败的翻译器
        mock_translator = AsyncMock()
        mock_translator.is_available.return_value = True
        mock_translator.translate.side_effect = Exception("Translation failed")
        
        translation_service.translators = {"failing": mock_translator}
        
        # 翻译应该返回原文而不是抛出异常
        result = await translation_service.translate("Hello", provider="failing")
        
        assert result.text == "Hello"
        assert result.result == "Hello"  # 应该返回原文
        assert result.provider == "fallback"
        
        await translation_service.cleanup()


@pytest.mark.asyncio
async def test_full_application_lifecycle():
    """测试完整应用生命周期"""
    core = SteamPlusCore()
    
    # 模拟所有外部依赖
    with patch('steamplus.service.translate.translation_service.initialize') as mock_translate_init, \
         patch('steamplus.service.translate.translation_service.cleanup') as mock_translate_cleanup, \
         patch('steamplus.service.steam.steam_connection.connect') as mock_steam_connect, \
         patch('steamplus.service.steam.steam_connection.disconnect') as mock_steam_disconnect, \
         patch.object(core, '_start_api_server') as mock_api_start, \
         patch('uvicorn.Server') as mock_server:
        
        # 设置模拟返回值
        mock_translate_init.return_value = None
        mock_translate_cleanup.return_value = None
        mock_steam_connect.return_value = True
        mock_steam_disconnect.return_value = None
        mock_api_start.return_value = None
        
        # 模拟 API 服务器任务
        mock_server_task = AsyncMock()
        core.api_server = mock_server_task
        
        try:
            # 初始化
            await core.initialize()
            assert core.settings is not None
            
            # 验证初始化调用
            mock_translate_init.assert_called_once()
            mock_api_start.assert_called_once()
            
            # 停止
            await core.stop()
            
            # 验证清理调用
            mock_translate_cleanup.assert_called_once()
            mock_steam_disconnect.assert_called_once()
            
        except Exception as e:
            # 确保即使出错也要清理
            await core.stop()
            raise


if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v"])
