"""
设备信息和工具模块

提供设备指纹、语言检测、系统信息等功能。
"""

import hashlib
import locale
import platform
import socket
import uuid
from pathlib import Path
from typing import Dict, Optional, Tuple

import psutil
from loguru import logger


class MachineInfo:
    """设备信息类"""
    
    def __init__(self):
        self._machine_id: Optional[str] = None
        self._system_info: Optional[Dict] = None
    
    @property
    def machine_id(self) -> str:
        """获取设备唯一标识"""
        if self._machine_id is None:
            self._machine_id = self._generate_machine_id()
        return self._machine_id
    
    def _generate_machine_id(self) -> str:
        """生成设备唯一标识"""
        try:
            # 使用多个硬件特征生成唯一标识
            factors = [
                platform.machine(),
                platform.processor(),
                str(uuid.getnode()),  # MAC 地址
                platform.system(),
                platform.release(),
            ]
            
            # 添加 CPU 信息
            try:
                import cpuinfo
                cpu_info = cpuinfo.get_cpu_info()
                factors.append(cpu_info.get('brand_raw', ''))
            except ImportError:
                pass
            
            # 组合所有因素并生成哈希
            combined = ''.join(factors).encode('utf-8')
            return hashlib.sha256(combined).hexdigest()[:16]
            
        except Exception as e:
            logger.warning(f"Failed to generate machine ID: {e}")
            # 回退到简单的 UUID
            return str(uuid.uuid4()).replace('-', '')[:16]
    
    @property
    def system_info(self) -> Dict:
        """获取系统信息"""
        if self._system_info is None:
            self._system_info = self._collect_system_info()
        return self._system_info
    
    def _collect_system_info(self) -> Dict:
        """收集系统信息"""
        try:
            info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'architecture': platform.architecture(),
                'hostname': socket.gethostname(),
                'python_version': platform.python_version(),
            }
            
            # 添加内存信息
            try:
                memory = psutil.virtual_memory()
                info['memory_total'] = memory.total
                info['memory_available'] = memory.available
            except Exception:
                pass
            
            # 添加 CPU 信息
            try:
                info['cpu_count'] = psutil.cpu_count()
                info['cpu_count_logical'] = psutil.cpu_count(logical=True)
            except Exception:
                pass
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to collect system info: {e}")
            return {}
    
    def get_language_info(self) -> Dict[str, str]:
        """获取语言信息"""
        try:
            # 获取系统语言设置
            default_locale = locale.getdefaultlocale()
            
            info = {
                'default_locale': default_locale[0] if default_locale[0] else 'en_US',
                'encoding': default_locale[1] if default_locale[1] else 'utf-8',
                'preferred_encoding': locale.getpreferredencoding(),
            }
            
            # 尝试获取更详细的语言信息
            try:
                import ctypes
                import ctypes.wintypes
                
                # Windows 特定的语言检测
                if platform.system() == 'Windows':
                    kernel32 = ctypes.windll.kernel32
                    lcid = kernel32.GetUserDefaultLCID()
                    info['windows_lcid'] = lcid
                    
                    # 获取语言名称
                    lang_name = ctypes.create_unicode_buffer(256)
                    kernel32.GetLocaleInfoW(lcid, 0x0002, lang_name, 256)  # LOCALE_SENGLANGUAGE
                    info['windows_language'] = lang_name.value
                    
            except Exception:
                pass
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get language info: {e}")
            return {'default_locale': 'en_US', 'encoding': 'utf-8'}
    
    def detect_steam_language(self) -> str:
        """检测 Steam 客户端语言"""
        try:
            # 尝试从 Steam 配置文件读取语言设置
            steam_paths = [
                Path.home() / "AppData/Local/Steam/config/loginusers.vdf",
                Path("C:/Program Files (x86)/Steam/config/loginusers.vdf"),
                Path("C:/Program Files/Steam/config/loginusers.vdf"),
            ]
            
            for path in steam_paths:
                if path.exists():
                    try:
                        with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            # 简单的语言检测逻辑
                            if 'schinese' in content.lower():
                                return 'zh-CN'
                            elif 'tchinese' in content.lower():
                                return 'zh-TW'
                            elif 'japanese' in content.lower():
                                return 'ja'
                            elif 'korean' in content.lower():
                                return 'ko'
                    except Exception:
                        continue
            
            # 回退到系统语言
            lang_info = self.get_language_info()
            default_locale = lang_info['default_locale']
            
            if default_locale.startswith('zh_CN'):
                return 'zh-CN'
            elif default_locale.startswith('zh_TW') or default_locale.startswith('zh_HK'):
                return 'zh-TW'
            elif default_locale.startswith('ja'):
                return 'ja'
            elif default_locale.startswith('ko'):
                return 'ko'
            else:
                return 'en'
                
        except Exception as e:
            logger.error(f"Failed to detect Steam language: {e}")
            return 'en'


class ProcessUtils:
    """进程工具类"""
    
    @staticmethod
    def find_steam_process() -> Optional[psutil.Process]:
        """查找 Steam 主进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'steam.exe' in proc.info['name'].lower():
                        # 确保是主进程而不是子进程
                        if proc.info['exe'] and 'steam.exe' in proc.info['exe'].lower():
                            return proc
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return None
        except Exception as e:
            logger.error(f"Failed to find Steam process: {e}")
            return None
    
    @staticmethod
    def get_steam_debug_port() -> Optional[int]:
        """获取 Steam CEF 调试端口"""
        try:
            # 首先尝试常用端口
            common_ports = [9222, 9223, 9224, 9225, 9226]
            for port in common_ports:
                if ProcessUtils.is_port_in_use(port):
                    logger.info(f"Found potential Steam debug port: {port}")
                    return port

            # 然后尝试从进程参数中查找
            steam_proc = ProcessUtils.find_steam_process()
            if steam_proc:
                try:
                    cmdline = steam_proc.cmdline()
                    for i, arg in enumerate(cmdline):
                        if '--remote-debugging-port' in arg:
                            if '=' in arg:
                                port_str = arg.split('=')[1]
                            elif i + 1 < len(cmdline):
                                port_str = cmdline[i + 1]
                            else:
                                continue

                            try:
                                port = int(port_str)
                                if ProcessUtils.is_port_in_use(port):
                                    return port
                            except ValueError:
                                continue
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 如果都没找到，返回默认端口（即使可能不可用）
            logger.warning("No Steam debug port found, Steam may not be running with remote debugging enabled")
            return None

        except Exception as e:
            logger.error(f"Failed to get Steam debug port: {e}")
            return None
    
    @staticmethod
    def is_port_in_use(port: int) -> bool:
        """检查端口是否被占用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                result = sock.connect_ex(('localhost', port))
                return result == 0
        except Exception:
            return False


# 全局实例
machine_info = MachineInfo()
