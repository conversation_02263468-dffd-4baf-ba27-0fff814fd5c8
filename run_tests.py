#!/usr/bin/env python3
"""
测试运行脚本

运行各种测试和检查。
"""

import sys
import subprocess
import asyncio
from pathlib import Path


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print('='*50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False


def check_dependencies():
    """检查依赖"""
    print("Checking dependencies...")
    
    required_packages = [
        'pyside6', 'playwright', 'fastapi', 'uvicorn', 
        'pydantic', 'aiohttp', 'keyring', 'psutil', 
        'loguru', 'click', 'pytest'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - NOT FOUND")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_unit_tests():
    """运行单元测试"""
    return run_command(
        "python -m pytest tests/ -v --tb=short",
        "Unit Tests"
    )


def run_linting():
    """运行代码检查"""
    success = True
    
    # 检查代码格式
    if not run_command("python -m black --check steamplus/", "Black Code Formatting Check"):
        print("Run 'python -m black steamplus/' to fix formatting issues")
        success = False
    
    # 检查导入排序
    if not run_command("python -m isort --check-only steamplus/", "Import Sorting Check"):
        print("Run 'python -m isort steamplus/' to fix import issues")
        success = False
    
    return success


def run_type_checking():
    """运行类型检查"""
    return run_command(
        "python -m mypy steamplus/ --ignore-missing-imports",
        "Type Checking"
    )


def test_basic_imports():
    """测试基本导入"""
    print("\nTesting basic imports...")
    
    try:
        from steamplus import main
        print("✅ steamplus.main")
        
        from steamplus.service.events import event_bus
        print("✅ steamplus.service.events")
        
        from steamplus.service.settings import get_settings
        print("✅ steamplus.service.settings")
        
        from steamplus.service.translate import translation_service
        print("✅ steamplus.service.translate")
        
        from steamplus.api.server import app
        print("✅ steamplus.api.server")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


async def test_async_components():
    """测试异步组件"""
    print("\nTesting async components...")
    
    try:
        from steamplus.service.events import event_bus
        
        # 测试事件总线
        await event_bus.initialize()
        print("✅ Event bus initialization")
        
        # 测试事件发布
        await event_bus.publish("test_event", {"test": "data"})
        print("✅ Event publishing")
        
        await event_bus.cleanup()
        print("✅ Event bus cleanup")
        
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False


def main():
    """主函数"""
    print("SteamPlus Test Runner")
    print("=" * 50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 12):
        print(f"❌ Python 3.12+ required, got {sys.version}")
        return 1
    
    print(f"✅ Python version: {sys.version}")
    
    # 检查项目结构
    project_root = Path(__file__).parent
    required_dirs = ['steamplus', 'tests']
    
    for dir_name in required_dirs:
        if not (project_root / dir_name).exists():
            print(f"❌ Required directory not found: {dir_name}")
            return 1
        print(f"✅ Directory exists: {dir_name}")
    
    # 运行测试
    tests = [
        ("Dependency Check", check_dependencies),
        ("Basic Imports", test_basic_imports),
        ("Async Components", lambda: asyncio.run(test_async_components())),
    ]
    
    # 可选测试（需要额外依赖）
    optional_tests = [
        ("Unit Tests", run_unit_tests),
        ("Code Linting", run_linting),
        ("Type Checking", run_type_checking),
    ]
    
    # 运行基本测试
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if not test_func():
                failed_tests.append(test_name)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed_tests.append(test_name)
    
    # 运行可选测试
    print(f"\n{'='*20} Optional Tests {'='*20}")
    
    for test_name, test_func in optional_tests:
        try:
            if not test_func():
                print(f"⚠️  {test_name} failed (optional)")
        except Exception as e:
            print(f"⚠️  {test_name} skipped: {e}")
    
    # 总结
    print(f"\n{'='*50}")
    print("Test Summary")
    print('='*50)
    
    if failed_tests:
        print(f"❌ Failed tests: {', '.join(failed_tests)}")
        return 1
    else:
        print("✅ All basic tests passed!")
        return 0


if __name__ == "__main__":
    sys.exit(main())
