"""
价格历史插件示例

展示如何创建 SteamPlus 插件。
"""

import asyncio
from typing import Dict, Any, Optional
from loguru import logger

from . import BasePlugin
from ..service.events import event_bus, Events


class PriceHistoryPlugin(BasePlugin):
    """价格历史插件"""
    
    name = "price_history"
    version = "1.0.0"
    description = "显示 Steam 游戏价格历史"
    author = "SteamPlus Team"
    
    def __init__(self):
        super().__init__()
        self.price_cache: Dict[str, Dict] = {}
        self.api_base_url = "https://api.steamprices.com"
    
    async def initialize(self) -> None:
        """插件初始化"""
        logger.info(f"Initializing {self.name} plugin v{self.version}")
        
        # 订阅 Steam 页面加载事件
        event_bus.subscribe(Events.STEAM_PAGE_LOADED, self._on_page_loaded)
        
        # 订阅翻译完成事件（可以在翻译结果中添加价格信息）
        event_bus.subscribe(Events.TRANSLATION_COMPLETED, self._on_translation_completed)
        
        logger.info(f"{self.name} plugin initialized")
    
    async def cleanup(self) -> None:
        """插件清理"""
        logger.info(f"Cleaning up {self.name} plugin")
        
        # 取消事件订阅
        event_bus.unsubscribe(Events.STEAM_PAGE_LOADED, self._on_page_loaded)
        event_bus.unsubscribe(Events.TRANSLATION_COMPLETED, self._on_translation_completed)
        
        # 清理缓存
        self.price_cache.clear()
        
        logger.info(f"{self.name} plugin cleaned up")
    
    async def _on_page_loaded(self, event) -> None:
        """页面加载事件处理"""
        try:
            page_data = event.data
            if not page_data:
                return
            
            page_type = page_data.get('page_type')
            url = page_data.get('url', '')
            
            # 只处理商店页面
            if page_type != 'store':
                return
            
            # 检查是否是游戏页面
            app_id = self._extract_app_id(url)
            if app_id:
                logger.debug(f"Detected game page: {app_id}")
                
                # 获取价格历史（在后台执行）
                asyncio.create_task(self._fetch_price_history(app_id))
                
        except Exception as e:
            logger.error(f"Error handling page loaded event: {e}")
    
    async def _on_translation_completed(self, event) -> None:
        """翻译完成事件处理"""
        try:
            # 这里可以在翻译结果中添加价格相关信息
            # 例如，如果翻译的是游戏名称，可以添加价格信息
            pass
        except Exception as e:
            logger.error(f"Error handling translation completed event: {e}")
    
    def _extract_app_id(self, url: str) -> Optional[str]:
        """从 URL 中提取游戏 ID"""
        try:
            # Steam 商店页面 URL 格式: https://store.steampowered.com/app/123456/game_name/
            if '/app/' in url:
                parts = url.split('/app/')
                if len(parts) > 1:
                    app_id = parts[1].split('/')[0]
                    if app_id.isdigit():
                        return app_id
            return None
        except Exception as e:
            logger.error(f"Error extracting app ID from URL {url}: {e}")
            return None
    
    async def _fetch_price_history(self, app_id: str) -> Optional[Dict]:
        """获取价格历史"""
        try:
            # 检查缓存
            if app_id in self.price_cache:
                logger.debug(f"Using cached price data for app {app_id}")
                return self.price_cache[app_id]
            
            # 模拟 API 调用（实际实现中应该调用真实的价格 API）
            logger.debug(f"Fetching price history for app {app_id}")
            
            # 模拟数据
            price_data = {
                'app_id': app_id,
                'current_price': 29.99,
                'lowest_price': 14.99,
                'highest_price': 59.99,
                'price_history': [
                    {'date': '2024-01-01', 'price': 59.99},
                    {'date': '2024-06-01', 'price': 29.99},
                    {'date': '2024-11-01', 'price': 14.99},
                ],
                'last_updated': '2024-12-01'
            }
            
            # 缓存数据
            self.price_cache[app_id] = price_data
            
            # 发布价格数据事件
            await event_bus.publish("price_history_updated", {
                'app_id': app_id,
                'price_data': price_data
            })
            
            logger.info(f"Price history fetched for app {app_id}: ${price_data['current_price']}")
            return price_data
            
        except Exception as e:
            logger.error(f"Error fetching price history for app {app_id}: {e}")
            return None
    
    async def get_price_data(self, app_id: str) -> Optional[Dict]:
        """获取价格数据（公共接口）"""
        if app_id in self.price_cache:
            return self.price_cache[app_id]
        
        return await self._fetch_price_history(app_id)
    
    def get_cached_apps(self) -> list:
        """获取已缓存的应用列表"""
        return list(self.price_cache.keys())
    
    def clear_cache(self) -> None:
        """清空价格缓存"""
        self.price_cache.clear()
        logger.info("Price cache cleared")
    
    async def on_steam_connected(self) -> None:
        """Steam 连接时的回调"""
        logger.info("Steam connected - Price history plugin ready")
    
    async def on_steam_disconnected(self) -> None:
        """Steam 断开时的回调"""
        logger.info("Steam disconnected - Price history plugin paused")
    
    def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式（用于设置界面）"""
        return {
            "enabled": {
                "type": "boolean",
                "default": True,
                "description": "启用价格历史功能"
            },
            "cache_size": {
                "type": "integer",
                "default": 100,
                "minimum": 10,
                "maximum": 1000,
                "description": "价格缓存大小"
            },
            "update_interval": {
                "type": "integer",
                "default": 3600,
                "minimum": 300,
                "maximum": 86400,
                "description": "价格更新间隔（秒）"
            }
        }
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """更新插件配置"""
        self.config.update(config)
        
        # 根据配置调整行为
        if "cache_size" in config:
            # 如果缓存大小变小，清理多余的缓存
            cache_size = config["cache_size"]
            if len(self.price_cache) > cache_size:
                # 保留最近使用的缓存项
                items = list(self.price_cache.items())
                self.price_cache = dict(items[-cache_size:])
        
        logger.info(f"Plugin config updated: {config}")


# 插件工厂函数（可选）
def create_plugin() -> PriceHistoryPlugin:
    """创建插件实例"""
    return PriceHistoryPlugin()
