"""
事件系统模块

基于 asyncio 的事件总线，支持异步事件发布和订阅。
"""

import asyncio
from typing import Any, Callable, Dict, List, Optional, Set
from dataclasses import dataclass
from loguru import logger


@dataclass
class Event:
    """事件数据类"""
    name: str
    data: Any = None
    source: Optional[str] = None
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            import time
            self.timestamp = time.time()


class EventBus:
    """异步事件总线"""
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = {}
        self._running = False
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._worker_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """初始化事件总线"""
        if self._running:
            return
        
        self._running = True
        self._worker_task = asyncio.create_task(self._event_worker())
        logger.info("Event bus initialized")
    
    async def cleanup(self) -> None:
        """清理事件总线"""
        if not self._running:
            return
        
        self._running = False
        
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        
        # 清空队列
        while not self._event_queue.empty():
            try:
                self._event_queue.get_nowait()
            except asyncio.QueueEmpty:
                break
        
        self._subscribers.clear()
        logger.info("Event bus cleaned up")
    
    def subscribe(self, event_name: str, callback: Callable) -> None:
        """订阅事件"""
        if event_name not in self._subscribers:
            self._subscribers[event_name] = []
        
        if callback not in self._subscribers[event_name]:
            self._subscribers[event_name].append(callback)
            logger.debug(f"Subscribed to event: {event_name}")
    
    def unsubscribe(self, event_name: str, callback: Callable) -> None:
        """取消订阅事件"""
        if event_name in self._subscribers:
            try:
                self._subscribers[event_name].remove(callback)
                logger.debug(f"Unsubscribed from event: {event_name}")
                
                # 如果没有订阅者了，删除事件
                if not self._subscribers[event_name]:
                    del self._subscribers[event_name]
            except ValueError:
                pass
    
    async def publish(self, event_name: str, data: Any = None, source: str = None) -> None:
        """发布事件"""
        if not self._running:
            logger.warning(f"Event bus not running, ignoring event: {event_name}")
            return
        
        event = Event(name=event_name, data=data, source=source)
        await self._event_queue.put(event)
        logger.debug(f"Published event: {event_name}")
    
    def publish_sync(self, event_name: str, data: Any = None, source: str = None) -> None:
        """同步发布事件（用于非异步上下文）"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果在事件循环中，使用 create_task
                asyncio.create_task(self.publish(event_name, data, source))
            else:
                # 如果不在事件循环中，直接运行
                loop.run_until_complete(self.publish(event_name, data, source))
        except RuntimeError:
            # 没有事件循环，创建新的
            asyncio.run(self.publish(event_name, data, source))
    
    async def _event_worker(self) -> None:
        """事件处理工作线程"""
        logger.info("Event worker started")
        
        while self._running:
            try:
                # 等待事件，设置超时避免阻塞
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._process_event(event)
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"Error in event worker: {e}")
        
        logger.info("Event worker stopped")
    
    async def _process_event(self, event: Event) -> None:
        """处理单个事件"""
        if event.name not in self._subscribers:
            return
        
        subscribers = self._subscribers[event.name].copy()
        
        # 并发执行所有订阅者的回调
        tasks = []
        for callback in subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    task = asyncio.create_task(callback(event))
                else:
                    # 同步回调在线程池中执行
                    task = asyncio.create_task(
                        asyncio.get_event_loop().run_in_executor(None, callback, event)
                    )
                tasks.append(task)
            except Exception as e:
                logger.error(f"Error creating task for callback: {e}")
        
        if tasks:
            # 等待所有任务完成，但不因为单个任务失败而停止
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error in event callback: {result}")
    
    def get_subscribers(self, event_name: str) -> List[Callable]:
        """获取事件的订阅者列表"""
        return self._subscribers.get(event_name, []).copy()
    
    def get_all_events(self) -> Set[str]:
        """获取所有已订阅的事件名称"""
        return set(self._subscribers.keys())
    
    @property
    def is_running(self) -> bool:
        """检查事件总线是否正在运行"""
        return self._running


# 全局事件总线实例
event_bus = EventBus()


# 常用事件名称常量
class Events:
    """事件名称常量"""
    
    # Steam 相关事件
    STEAM_CONNECTED = "steam_connected"
    STEAM_DISCONNECTED = "steam_disconnected"
    STEAM_PAGE_LOADED = "steam_page_loaded"
    
    # 翻译相关事件
    TRANSLATION_STARTED = "translation_started"
    TRANSLATION_COMPLETED = "translation_completed"
    TRANSLATION_FAILED = "translation_failed"
    
    # 设置相关事件
    SETTINGS_CHANGED = "settings_changed"
    SETTINGS_LOADED = "settings_loaded"
    
    # 应用程序事件
    APP_STARTED = "app_started"
    APP_STOPPING = "app_stopping"
    APP_ERROR = "app_error"
    
    # 插件事件
    PLUGIN_LOADED = "plugin_loaded"
    PLUGIN_UNLOADED = "plugin_unloaded"
    PLUGIN_ERROR = "plugin_error"
