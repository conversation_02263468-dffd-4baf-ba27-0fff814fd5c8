下面给出一份 SteamPlus 的 Python 化、简化重构方案（架构蓝图 + 目录组织 + 关键组件说明）。设计目标是在完整保留「即时翻译、多引擎切换、浏览器级脚本注入、系统托盘、自动更新」等核心体验的同时，显著降低依赖链、提升可维护性，并使其易于二次开发。

1. 总体设计理念
维度	SteamMeta（参考项目）	SteamPlus（重构后）
语言	Go + Wails	Python 3.12
架构风格	单体 GUI 应用（嵌入 Web 前端）	分层 + 插件化（核心守护进程 + 可选 UI）
GUI	Wails WebView2 + Systray	PySide6（系统托盘 + 设置窗口，可选）
内嵌 HTTP API	自定义 Gin 服务器	FastAPI（OpenAPI 文档、热重载友好）
Steam 注入方式	CEF 远端调试 + JS 插件 
GitHub
Playwright‑CDP 注入 / 原生 CEF Hook（可选）
自动更新	go‑selfupdate + 自建服务	PyUpdater + GitHub Releases / S3
设置存储	本地 YAML + Keyring	Pydantic + SQLite + keyring
事件埋点	自建 analytics	预留接口，默认仅本地日志

2. 逻辑分层
arduino
复制
┌────────────────────┐
│    steamplus.ui    │   ← PySide6 / Tk (可选)
└────────┬───────────┘
         │
┌────────▼───────────┐
│  steamplus.service │   ← “业务微内核”，纯 Python 包
│  ├─ steam.py       │   Steam 会话管理 / CDP 注入
│  ├─ translate.py   │   统一翻译适配层
│  ├─ settings.py    │   Pydantic + SQLite
│  ├─ updater.py     │   PyUpdater 封装
│  ├─ machine.py     │   设备指纹、语言检测
│  └─ events.py      │   基于 asyncio.EventBus
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.api      │   ← FastAPI  +  Uvicorn
│  (same process)    │
└────────┬───────────┘
         │
┌────────▼───────────┐
│ steamplus.plugins  │   ← 第三方功能热插拔 (entry_points)
└────────────────────┘
守护进程 + 可选 UI
• 服务进程始终常驻（Windows 下注册为后台任务），负责与 Steam CEF 会话交互。
• 托盘 / 设置窗口可在需要时启动，也可完全移除以减小体积。

3. 目录结构示例
bash
复制
steamplus/
├─ steamplus/
│  ├─ __init__.py
│  ├─ core.py           # 入口 & CLI
│  ├─ service/
│  │   ├─ steam.py
│  │   ├─ translate.py
│  │   ├─ settings.py
│  │   ├─ updater.py
│  │   ├─ machine.py
│  │   └─ events.py
│  ├─ api/
│  │   └─ server.py
│  ├─ ui/
│  │   ├─ tray.py
│  │   └─ settings_dialog.py
│  ├─ plugins/
│  │   ├─ __init__.py
│  │   └─ price_history.py  # 示例插件
│  └─ resources/
└─ pyproject.toml
4. 关键模块设计
4.1 steamplus.service.steam
Playwright‑CDP：比纯 pyppeteer 更稳定、维护活跃。

连接流程

查找 Steam.exe 进程 → 读取 steam.pid 里的 CEF 远端调试端口（与 Meta 一致 
GitHub
）。

async with playw.chromium.connect_over_cdp(...) 建立会话。

为匹配的 Steam 页面注入 Content Script（翻译面板、划词监听）。

通过 EventBus 将连接状态推送给 UI。

4.2 steamplus.service.translate
统一包装主流翻译商：

Provider	SDK / 调用方式
Bing	unofficial REST
DeepL	python‑deepl
Google	googletrans（备用）
NiuTrans	官方 API

选择算法：优先使用免费额度 → 按用户偏好轮询。

4.3 steamplus.service.settings
python
复制
from pydantic import BaseModel, Field

class General(BaseModel):
    autostart: bool = True
    channel: str = "stable"

class Translate(BaseModel):
    provider: str = "bing"
    auto_detect: bool = True

class Settings(BaseModel):
    general: General = Field(default_factory=General)
    translate: Translate = Field(default_factory=Translate)
序列化到 SQLite (app_state.db)；安全字段（token、api_key）存入 OS keyring。

变更后自动触发 events.publish("settings_changed", ...)。

4.4 steamplus.api.server
python
复制
app = FastAPI(
    title="SteamPlus Local API",
    version="0.1.0",
    docs_url="/",
)

@app.get("/status")
async def status():
    return service.steam.current_status()

@app.post("/translate")
async def translate(text: str):
    return service.translate.translate(text)
供前端页面 / 插件 或外部脚本调用。Uvicorn 嵌入同进程，不需要额外服务端口映射。

4.5 自动更新
PyUpdater + GitHub Releases：生成差分包，支持 Windows x64 单文件。

后台循环检测 /latest.json → 下载 → 验签 → 提示托盘气泡 / 静默热更新。

5. 运行时流程
text
复制
+---------+         +------------------+         +-------------+
|  User   | ───UI──►| steamplus.ui     |──event──►             |
+---------+         |  (optional)      |         |             |
                    +------------------+         |             |
                                                  v
                                         +------------------+
                                         | steamplus.service|
                                         |  • steam         |
                                         |  • translate     |
                                         |  • settings      |
                                         |  • updater       |
                                         +------------------+
                                                  │
                                                  │ FastAPI
                                                  ▼
                                         +------------------+
                                         |  localhost:50055 |
                                         +------------------+
6. 安装与打包
bash
复制
# 开发
pip install -e .[dev]
uvicorn steamplus.api.server:app --reload

# 生成发行包
pyinstaller --noconsole --onefile steamplus/core.py
pyupdater pkg --process --sign
开机自启：写入 HKCU\Software\Microsoft\Windows\CurrentVersion\Run。

全局依赖仅：pyside6, playwright, fastapi, uvicorn, pydantic, pyinstaller, pyupdater.

7. 可扩展性
插件系统：使用 entry_points={"steamplus.plugins": ...}。任何包声明 class Plugin(BasePlugin): ... 即可热加载。

事件总线：基于 asyncio，跨插件通讯零耦合。

翻译引擎：Strategy Pattern，新增引擎只需实现 BaseTranslator.

8. 与 SteamMeta 的差异 / 优势
依赖 & 编译成本更低：纯 Python ，摆脱 Go/Wails 交叉编译链。

后端 / 前端解耦：FastAPI 暴露接口，UI 可以替换为 React / Flutter / 甚至 CLI。

可测试性提升：每个 service 都是独立协程，可直接 pytest‑asyncio。

跨平台潜力：Playwright/Qt 在 Linux Steam Deck 上也能运行，后续可原生适配。

9. 下一步最小可行实现（MVP）
核心守护进程

Steam 会话检测 + JS 注入「划词翻译」

Settings → SQLite + keyring

翻译服务：Bing 免费接口

系统托盘：翻译开关 / 设置 / 退出

FastAPI /status /translate

自更新：检测 GitHub latest tag，弹窗提示下载

"""系统托盘界面"""
import asyncio
import sys
from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import (QApplication, QSystemTrayIcon, QMenu, 
                               QMessageBox, QDialog, QVBoxLayout, QLabel)
from PySide6.QtCore import QTimer, QThread, Signal, QObject
from PySide6.QtGui import QIcon, QAction
import logging

from ..service.events import event_bus
from ..service.settings import get_settings
from ..service.steam import steam_connection
from .settings_dialog import SettingsDialog

logger = logging.getLogger(__name__)

class EventHandler(QObject):
    """事件处理器，用于在主线程中处理异步事件"""
    
    steam_connected = Signal()
    steam_disconnected = Signal()
    translation_completed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # 订阅事件
        event_bus.subscribe("steam_connected", self._on_steam_connected)
        event_bus.subscribe("steam_disconnected", self._on_steam_disconnected)
        event_bus.subscribe("translation_completed", self._on_translation_completed)
    
    async def _on_steam_connected(self, event):
        """Steam 连接事件"""
        self.steam_connected.emit()
    
    async def _on_steam_disconnected(self, event):
        """Steam 断开事件"""
        self.steam_disconnected.emit()
    
    async def _on_translation_completed(self, event):
        """翻译完成事件"""
        self.translation_completed.emit(event.data)

class SystemTrayIcon(QSystemTrayIcon):
    """系统托盘图标"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.settings_dialog: Optional[SettingsDialog] = None
        self.event_handler = EventHandler()
        self.translation_enabled = True
        
        # 设置图标
        self.setIcon(self._create_icon())
        
        # 创建菜单
        self.create_menu()
        
        # 连接信号
        self.activated.connect(self.on_tray_activated)
        self.event_handler.steam_connected.connect(self.on_steam_connected)
        self.event_handler.steam_disconnected.connect(self.on_steam_disconnected)
        self.event_handler.translation_completed.connect(self.on_translation_completed)
        
        # 设置提示文本
        self.setToolTip("SteamPlus - Steam 即时翻译")
        
        # 显示启动消息
        if self.isSystemTrayAvailable():
            self.showMessage(
                "SteamPlus",
                "SteamPlus 已启动，正在等待 Steam 连接...",
                QSystemTrayIcon.MessageIcon.Information,
                3000
            )
    
    def _create_icon(self) -> QIcon:
        """创建托盘图标"""
        # 这里应该使用实际的图标文件
        # 暂时使用系统默认图标
        return self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon)
    
    def create_menu(self):
        """创建右键菜单"""
        menu = QMenu()
        
        # 翻译开关
        self.toggle_action = QAction("禁用翻译", self)
        self.toggle_action.triggered.connect(self.toggle_translation)
        menu.addAction(self.toggle_action)
        
        menu.addSeparator()
        
        # Steam 连接状态
        self.status_action = QAction("Steam: 未连接", self)
        self.status_action.setEnabled(False)
        menu.addAction(self.status_action)
        
        menu.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        menu.addAction(settings_action)
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        menu.addAction(about_action)
        
        menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application)
        menu.addAction(quit_action)
        
        self.setContextMenu(menu)
    
    def on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_settings()
    
    def toggle_translation(self):
        """切换翻译功能"""
        self.translation_enabled = not self.translation_enabled
        
        if self.translation_enabled:
            self.toggle_action.setText("禁用翻译")
            self.showMessage("SteamPlus", "翻译功能已启用", 
                           QSystemTrayIcon.MessageIcon.Information, 2000)
        else:
            self.toggle_action.setText("启用翻译")
            self.showMessage("SteamPlus", "翻译功能已禁用", 
                           QSystemTrayIcon.MessageIcon.Warning, 2000)
    
    def show_settings(self):
        """显示设置对话框"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog()
        
        self.settings_dialog.show()
        self.settings_dialog.raise_()
        self.settings_dialog.activateWindow()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            None,
            "关于 SteamPlus",
            """
            <h3>SteamPlus v0.1.0</h3>
            <p>Steam 即时翻译工具</p>
            <p>支持划词翻译、多引擎切换等功能</p>
            <p><a href="https://github.com/steamplus/steamplus">GitHub</a></p>
            """
        )
    
    def quit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            None,
            "确认退出",
            "确定要退出 SteamPlus 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            QApplication.quit()
    
    def on_steam_connected(self):
        """Steam 连接成功"""
        self.status_action.setText("Steam: 已连接")
        self.showMessage(
            "SteamPlus",
            "Steam 连接成功，翻译功能已就绪",
            QSystemTrayIcon.MessageIcon.Information,
            3000
        )
    
    def on_steam_disconnected(self):
        """Steam 连接断开"""
        self.status_action.setText("Steam: 未连接")
        self.showMessage(
            "SteamPlus",
            "Steam 连接断开，正在重新连接...",
            QSystemTrayIcon.MessageIcon.Warning,
            3000
        )
    
    def on_translation_completed(self, data):
        """翻译完成"""
        if not self.translation_enabled:
            return
        
        # 可以在这里显示翻译通知（可选）
        pass

class TrayApplication:
    """托盘应用程序"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.tray_icon: Optional[SystemTrayIcon] = None
        self.running = False
    
    def start(self):
        """启动托盘应用"""
        if self.running:
            return
        
        # 创建 QApplication
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)
        
        # 检查系统托盘支持
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(
                None,
                "系统托盘",
                "系统不支持托盘功能"
            )
            return False
        
        # 创建托盘图标
        self.tray_icon = SystemTrayIcon()
        self.tray_icon.show()
        
        self.running = True
        logger.info("System tray started")
        return True
    
    def stop(self):
        """停止托盘应用"""
        if not self.running:
            return
        
        if self.tray_icon:
            self.tray_icon.hide()
            self.tray_icon = None
        
        self.running = False
        logger.info("System tray stopped")
    
    def exec(self):
        """运行应用程序事件循环"""
        if self.app and self.running:
            return self.app.exec()
        return 0

# 全局托盘应用实例
tray_app = TrayApplication()


"""系统托盘界面"""
import asyncio
import sys
from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import (QApplication, QSystemTrayIcon, QMenu, 
                               QMessageBox, QDialog, QVBoxLayout, QLabel)
from PySide6.QtCore import QTimer, QThread, Signal, QObject
from PySide6.QtGui import QIcon, QAction
import logging

from ..service.events import event_bus
from ..service.settings import get_settings
from ..service.steam import steam_connection
from .settings_dialog import SettingsDialog

logger = logging.getLogger(__name__)

class EventHandler(QObject):
    """事件处理器，用于在主线程中处理异步事件"""
    
    steam_connected = Signal()
    steam_disconnected = Signal()
    translation_completed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # 订阅事件
        event_bus.subscribe("steam_connected", self._on_steam_connected)
        event_bus.subscribe("steam_disconnected", self._on_steam_disconnected)
        event_bus.subscribe("translation_completed", self._on_translation_completed)
    
    async def _on_steam_connected(self, event):
        """Steam 连接事件"""
        self.steam_connected.emit()
    
    async def _on_steam_disconnected(self, event):
        """Steam 断开事件"""
        self.steam_disconnected.emit()
    
    async def _on_translation_completed(self, event):
        """翻译完成事件"""
        self.translation_completed.emit(event.data)

class SystemTrayIcon(QSystemTrayIcon):
    """系统托盘图标"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.settings_dialog: Optional[SettingsDialog] = None
        self.event_handler = EventHandler()
        self.translation_enabled = True
        
        # 设置图标
        self.setIcon(self._create_icon())
        
        # 创建菜单
        self.create_menu()
        
        # 连接信号
        self.activated.connect(self.on_tray_activated)
        self.event_handler.steam_connected.connect(self.on_steam_connected)
        self.event_handler.steam_disconnected.connect(self.on_steam_disconnected)
        self.event_handler.translation_completed.connect(self.on_translation_completed)
        
        # 设置提示文本
        self.setToolTip("SteamPlus - Steam 即时翻译")
        
        # 显示启动消息
        if self.isSystemTrayAvailable():
            self.showMessage(
                "SteamPlus",
                "SteamPlus 已启动，正在等待 Steam 连接...",
                QSystemTrayIcon.MessageIcon.Information,
                3000
            )
    
    def _create_icon(self) -> QIcon:
        """创建托盘图标"""
        # 这里应该使用实际的图标文件
        # 暂时使用系统默认图标
        return self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon)
    
    def create_menu(self):
        """创建右键菜单"""
        menu = QMenu()
        
        # 翻译开关
        self.toggle_action = QAction("禁用翻译", self)
        self.toggle_action.triggered.connect(self.toggle_translation)
        menu.addAction(self.toggle_action)
        
        menu.addSeparator()
        
        # Steam 连接状态
        self.status_action = QAction("Steam: 未连接", self)
        self.status_action.setEnabled(False)
        menu.addAction(self.status_action)
        
        menu.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        menu.addAction(settings_action)
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        menu.addAction(about_action)
        
        menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application)
        menu.addAction(quit_action)
        
        self.setContextMenu(menu)
    
    def on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_settings()
    
    def toggle_translation(self):
        """切换翻译功能"""
        self.translation_enabled = not self.translation_enabled
        
        if self.translation_enabled:
            self.toggle_action.setText("禁用翻译")
            self.showMessage("SteamPlus", "翻译功能已启用", 
                           QSystemTrayIcon.MessageIcon.Information, 2000)
        else:
            self.toggle_action.setText("启用翻译")
            self.showMessage("SteamPlus", "翻译功能已禁用", 
                           QSystemTrayIcon.MessageIcon.Warning, 2000)
    
    def show_settings(self):
        """显示设置对话框"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog()
        
        self.settings_dialog.show()
        self.settings_dialog.raise_()
        self.settings_dialog.activateWindow()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            None,
            "关于 SteamPlus",
            """
            <h3>SteamPlus v0.1.0</h3>
            <p>Steam 即时翻译工具</p>
            <p>支持划词翻译、多引擎切换等功能</p>
            <p><a href="https://github.com/steamplus/steamplus">GitHub</a></p>
            """
        )
    
    def quit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            None,
            "确认退出",
            "确定要退出 SteamPlus 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            QApplication.quit()
    
    def on_steam_connected(self):
        """Steam 连接成功"""
        self.status_action.setText("Steam: 已连接")
        self.showMessage(
            "SteamPlus",
            "Steam 连接成功，翻译功能已就绪",
            QSystemTrayIcon.MessageIcon.Information,
            3000
        )
    
    def on_steam_disconnected(self):
        """Steam 连接断开"""
        self.status_action.setText("Steam: 未连接")
        self.showMessage(
            "SteamPlus",
            "Steam 连接断开，正在重新连接...",
            QSystemTrayIcon.MessageIcon.Warning,
            3000
        )
    
    def on_translation_completed(self, data):
        """翻译完成"""
        if not self.translation_enabled:
            return
        
        # 可以在这里显示翻译通知（可选）
        pass

class TrayApplication:
    """托盘应用程序"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.tray_icon: Optional[SystemTrayIcon] = None
        self.running = False
    
    def start(self):
        """启动托盘应用"""
        if self.running:
            return
        
        # 创建 QApplication
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)
        
        # 检查系统托盘支持
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(
                None,
                "系统托盘",
                "系统不支持托盘功能"
            )
            return False
        
        # 创建托盘图标
        self.tray_icon = SystemTrayIcon()
        self.tray_icon.show()
        
        self.running = True
        logger.info("System tray started")
        return True
    
    def stop(self):
        """停止托盘应用"""
        if not self.running:
            return
        
        if self.tray_icon:
            self.tray_icon.hide()
            self.tray_icon = None
        
        self.running = False
        logger.info("System tray stopped")
    
    def exec(self):
        """运行应用程序事件循环"""
        if self.app and self.running:
            return self.app.exec()
        return 0

# 全局托盘应用实例
tray_app = TrayApplication()
