#!/usr/bin/env python3
"""
SteamPlus 开箱可用启动脚本

用户友好的启动方式，无需复杂配置。
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def start_easy_mode():
    """开箱可用模式启动"""
    print("🎮 SteamPlus - 开箱可用模式")
    print("=" * 50)
    
    try:
        # 导入核心模块
        from steamplus.service.events import event_bus
        from steamplus.service.steam import steam_connection
        from steamplus.service.standalone_translate import standalone_translate_service
        from steamplus.api.server import app as api_app
        
        print("📋 初始化事件系统...")
        await event_bus.initialize()
        
        print("🔍 智能连接 Steam...")
        steam_connected = await steam_connection.connect()
        
        if steam_connected:
            if steam_connection._connection_mode == "cdp":
                print("✅ Steam CDP 连接成功！可以使用页面翻译功能")
            elif steam_connection._connection_mode == "browser":
                print("✅ 独立浏览器模式启动！在新打开的浏览器中访问 Steam")
            elif steam_connection._connection_mode == "standalone":
                print("✅ 独立翻译模式启动！可以使用剪贴板翻译功能")
        else:
            print("⚠️  Steam 连接失败，但不影响翻译功能使用")
        
        print("🔤 启动独立翻译服务...")
        await standalone_translate_service.initialize()
        
        print("🌐 启动 API 服务器...")
        import uvicorn
        from steamplus.service.settings import get_settings
        
        settings = get_settings()
        port = settings.general.api_port
        
        # 在后台启动 API 服务器
        config = uvicorn.Config(
            app=api_app,
            host="127.0.0.1",
            port=port,
            log_level="warning"  # 减少日志输出
        )
        server = uvicorn.Server(config)
        api_task = asyncio.create_task(server.serve())
        
        print("\n🎉 SteamPlus 启动成功！")
        print("=" * 50)
        print("📡 API 服务: http://localhost:{}".format(port))
        print("📖 API 文档: http://localhost:{}/docs".format(port))
        
        # 显示功能状态
        print("\n💡 可用功能:")
        if steam_connection._connection_mode == "cdp":
            print("   ✅ Steam 页面翻译 - 在 Steam 中选中文本即可翻译")
        elif steam_connection._connection_mode == "browser":
            print("   ✅ 浏览器翻译 - 在打开的浏览器中访问 Steam")
        
        if standalone_translate_service.running:
            print("   ✅ 剪贴板翻译 - 复制文本自动翻译")
            print("   ✅ API 翻译 - 通过 HTTP API 调用翻译")
        
        print("\n🔧 使用说明:")
        print("   - 复制英文文本到剪贴板，会自动翻译并显示通知")
        print("   - 访问 API 文档页面可以测试翻译功能")
        print("   - 按 Ctrl+C 退出程序")
        
        # 启动系统托盘（如果可用）
        try:
            from steamplus.ui import tray_app
            if tray_app.start():
                print("   ✅ 系统托盘已启动 - 查看托盘图标进行控制")
                
                # 运行 Qt 事件循环
                tray_task = asyncio.create_task(
                    asyncio.get_event_loop().run_in_executor(None, tray_app.exec)
                )
                
                # 等待任一任务完成
                done, pending = await asyncio.wait(
                    [api_task, tray_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消其他任务
                for task in pending:
                    task.cancel()
            else:
                print("   ⚠️  系统托盘启动失败，使用命令行模式")
                await api_task
                
        except ImportError:
            print("   ⚠️  系统托盘不可用，使用命令行模式")
            await api_task
        
    except KeyboardInterrupt:
        print("\n⏹️  收到退出信号...")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        print("🧹 清理资源...")
        try:
            await standalone_translate_service.cleanup()
            await steam_connection.disconnect()
            await event_bus.cleanup()
        except:
            pass
        print("👋 再见！")


def show_welcome():
    """显示欢迎信息"""
    print("""
🎮 欢迎使用 SteamPlus！

SteamPlus 是一个智能的 Steam 翻译工具，支持多种翻译模式：

🔥 主要功能:
   • Steam 页面即时翻译
   • 剪贴板自动翻译  
   • 多引擎翻译支持
   • HTTP API 接口
   • 系统托盘控制

🚀 开箱可用:
   • 无需复杂配置
   • 自动检测 Steam
   • 智能连接策略
   • 多种备用方案

💡 使用提示:
   • 首次运行会自动配置
   • 支持 Bing、Google 等翻译引擎
   • 可通过 API 集成到其他应用

按任意键开始启动...
""")
    
    try:
        input()
    except:
        pass


def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def install_missing_dependencies():
    """安装缺失的依赖"""
    missing_deps = []
    
    # 检查核心依赖
    core_deps = ['loguru', 'click', 'pydantic', 'fastapi', 'uvicorn', 'aiohttp']
    for dep in core_deps:
        try:
            __import__(dep.replace('-', '_'))
        except ImportError:
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"⚠️  检测到缺失依赖: {', '.join(missing_deps)}")
        print("正在自动安装...")
        
        try:
            import subprocess
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_deps)
            print("✅ 依赖安装完成")
            return True
        except Exception as e:
            print(f"❌ 自动安装失败: {e}")
            print("请手动运行: pip install " + " ".join(missing_deps))
            return False
    
    return True


def main():
    """主函数"""
    # 检查 Python 版本
    if not check_python_version():
        return 1
    
    # 显示欢迎信息
    show_welcome()
    
    # 检查并安装依赖
    if not install_missing_dependencies():
        return 1
    
    # 启动应用
    try:
        asyncio.run(start_easy_mode())
        return 0
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
        return 0
    except Exception as e:
        print(f"\n💥 启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
