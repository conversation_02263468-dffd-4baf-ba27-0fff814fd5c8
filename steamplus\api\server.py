"""
FastAPI 服务器

提供本地 HTTP API 接口，支持翻译、状态查询、设置管理等功能。
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from loguru import logger

from ..service.translate import translation_service, TranslationResult
from ..service.steam import steam_connection
from ..service.settings import get_settings, save_settings, Settings
from ..service.events import event_bus, Events
from ..service.machine import machine_info


# API 模型定义
class TranslationRequest(BaseModel):
    """翻译请求模型"""
    text: str = Field(..., description="要翻译的文本", min_length=1, max_length=5000)
    source_lang: str = Field(default="auto", description="源语言")
    target_lang: str = Field(default="zh-CN", description="目标语言")
    provider: Optional[str] = Field(default=None, description="翻译提供商")


class TranslationResponse(BaseModel):
    """翻译响应模型"""
    text: str
    result: str
    source_lang: str
    target_lang: str
    provider: str
    confidence: float = 0.0
    cached: bool = False
    duration: float = 0.0


class StatusResponse(BaseModel):
    """状态响应模型"""
    status: str
    version: str
    uptime: float
    steam_connected: bool
    steam_pages: int
    translation_providers: List[str]
    settings_loaded: bool


class SettingsResponse(BaseModel):
    """设置响应模型"""
    general: Dict[str, Any]
    translate: Dict[str, Any]
    steam: Dict[str, Any]
    ui: Dict[str, Any]


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str
    detail: Optional[str] = None
    code: Optional[str] = None


# 创建 FastAPI 应用
app = FastAPI(
    title="SteamPlus Local API",
    description="SteamPlus 本地 API 服务，提供翻译和状态查询功能",
    version="0.1.0",
    docs_url="/",
    redoc_url="/redoc",
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 本地服务，允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 应用启动时间
_start_time = datetime.now()


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("FastAPI server starting...")
    
    # 初始化翻译服务
    try:
        await translation_service.initialize()
        logger.info("Translation service initialized")
    except Exception as e:
        logger.error(f"Failed to initialize translation service: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("FastAPI server shutting down...")
    
    # 清理翻译服务
    try:
        await translation_service.cleanup()
        logger.info("Translation service cleaned up")
    except Exception as e:
        logger.error(f"Failed to cleanup translation service: {e}")


@app.get("/status", response_model=StatusResponse)
async def get_status():
    """获取服务状态"""
    try:
        uptime = (datetime.now() - _start_time).total_seconds()
        
        return StatusResponse(
            status="running",
            version="0.1.0",
            uptime=uptime,
            steam_connected=steam_connection.is_connected,
            steam_pages=len(steam_connection.pages),
            translation_providers=translation_service.get_available_providers(),
            settings_loaded=True
        )
    except Exception as e:
        logger.error(f"Failed to get status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """翻译文本"""
    try:
        # 发布翻译开始事件
        await event_bus.publish(Events.TRANSLATION_STARTED, {
            'text': request.text,
            'source_lang': request.source_lang,
            'target_lang': request.target_lang,
            'provider': request.provider
        })
        
        # 执行翻译
        result = await translation_service.translate(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            provider=request.provider
        )
        
        # 发布翻译完成事件
        await event_bus.publish(Events.TRANSLATION_COMPLETED, {
            'text': request.text,
            'result': result.result,
            'provider': result.provider
        })
        
        return TranslationResponse(
            text=result.text,
            result=result.result,
            source_lang=result.source_lang,
            target_lang=result.target_lang,
            provider=result.provider,
            confidence=result.confidence,
            cached=result.cached,
            duration=result.duration
        )
        
    except Exception as e:
        logger.error(f"Translation failed: {e}")
        
        # 发布翻译失败事件
        await event_bus.publish(Events.TRANSLATION_FAILED, {
            'text': request.text,
            'error': str(e)
        })
        
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/translate/providers")
async def get_translation_providers():
    """获取可用的翻译提供商"""
    try:
        providers = translation_service.get_available_providers()
        return {"providers": providers}
    except Exception as e:
        logger.error(f"Failed to get translation providers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/translate/cache")
async def clear_translation_cache():
    """清空翻译缓存"""
    try:
        translation_service.clear_cache()
        return {"message": "Translation cache cleared"}
    except Exception as e:
        logger.error(f"Failed to clear translation cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/settings", response_model=SettingsResponse)
async def get_settings_api():
    """获取设置"""
    try:
        settings = get_settings()
        return SettingsResponse(
            general=settings.general.model_dump(),
            translate=settings.translate.model_dump(),
            steam=settings.steam.model_dump(),
            ui=settings.ui.model_dump()
        )
    except Exception as e:
        logger.error(f"Failed to get settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/settings")
async def update_settings_api(settings_data: Dict[str, Any]):
    """更新设置"""
    try:
        # 获取当前设置
        current_settings = get_settings()
        
        # 更新设置
        updated_data = current_settings.model_dump()
        updated_data.update(settings_data)
        
        # 验证并保存
        new_settings = Settings(**updated_data)
        save_settings(new_settings)
        
        return {"message": "Settings updated successfully"}
        
    except Exception as e:
        logger.error(f"Failed to update settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/steam/status")
async def get_steam_status():
    """获取 Steam 连接状态"""
    try:
        return {
            "connected": steam_connection.is_connected,
            "pages": len(steam_connection.pages),
            "debug_port": steam_connection._debug_port
        }
    except Exception as e:
        logger.error(f"Failed to get Steam status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/steam/connect")
async def connect_steam(background_tasks: BackgroundTasks):
    """连接到 Steam"""
    try:
        if steam_connection.is_connected:
            return {"message": "Already connected to Steam"}
        
        # 在后台任务中执行连接
        background_tasks.add_task(steam_connection.connect)
        
        return {"message": "Steam connection initiated"}
        
    except Exception as e:
        logger.error(f"Failed to connect to Steam: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/steam/disconnect")
async def disconnect_steam(background_tasks: BackgroundTasks):
    """断开 Steam 连接"""
    try:
        if not steam_connection.is_connected:
            return {"message": "Not connected to Steam"}
        
        # 在后台任务中执行断开
        background_tasks.add_task(steam_connection.disconnect)
        
        return {"message": "Steam disconnection initiated"}
        
    except Exception as e:
        logger.error(f"Failed to disconnect from Steam: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/steam/pages")
async def get_steam_pages():
    """获取 Steam 页面列表"""
    try:
        pages_info = []
        for page_id, page in steam_connection.pages.items():
            try:
                pages_info.append({
                    "id": page_id,
                    "url": page.url,
                    "title": await page.title() if page else "Unknown"
                })
            except Exception:
                pages_info.append({
                    "id": page_id,
                    "url": "Unknown",
                    "title": "Unknown"
                })
        
        return {"pages": pages_info}
        
    except Exception as e:
        logger.error(f"Failed to get Steam pages: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/system/info")
async def get_system_info():
    """获取系统信息"""
    try:
        return {
            "machine_id": machine_info.machine_id,
            "system_info": machine_info.system_info,
            "language_info": machine_info.get_language_info()
        }
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc)
        ).model_dump()
    )
