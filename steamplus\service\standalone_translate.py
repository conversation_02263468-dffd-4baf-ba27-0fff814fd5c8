"""
独立翻译服务

无需 Steam 连接的翻译功能，支持：
1. 剪贴板监控翻译
2. 快捷键翻译
3. 浮动翻译窗口
4. 网页翻译助手
"""

import asyncio
import time
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False

from loguru import logger

from .events import event_bus, Events
from .translate import translation_service
from .settings import get_settings


class StandaloneTranslateService:
    """独立翻译服务"""
    
    def __init__(self):
        self.running = False
        self.clipboard_monitor_task: Optional[asyncio.Task] = None
        self.last_clipboard_text = ""
        self.translation_window = None
        
        # 翻译历史
        self.translation_history: list = []
        self.max_history = 100
    
    async def initialize(self) -> None:
        """初始化独立翻译服务"""
        logger.info("初始化独立翻译服务...")
        
        # 初始化翻译服务
        await translation_service.initialize()
        
        # 启动剪贴板监控
        if CLIPBOARD_AVAILABLE:
            await self.start_clipboard_monitor()
        else:
            logger.warning("剪贴板功能不可用，请安装 pyperclip: pip install pyperclip")
        
        # 创建翻译快捷方式
        await self._create_translation_shortcuts()
        
        self.running = True
        logger.info("✅ 独立翻译服务已启动")
    
    async def cleanup(self) -> None:
        """清理服务"""
        self.running = False
        
        if self.clipboard_monitor_task:
            self.clipboard_monitor_task.cancel()
            try:
                await self.clipboard_monitor_task
            except asyncio.CancelledError:
                pass
        
        if self.translation_window:
            try:
                self.translation_window.close()
            except:
                pass
        
        await translation_service.cleanup()
        logger.info("独立翻译服务已停止")
    
    async def start_clipboard_monitor(self) -> None:
        """启动剪贴板监控"""
        if not CLIPBOARD_AVAILABLE:
            return
        
        logger.info("启动剪贴板翻译监控...")
        self.clipboard_monitor_task = asyncio.create_task(self._clipboard_monitor_loop())
    
    async def _clipboard_monitor_loop(self) -> None:
        """剪贴板监控循环"""
        while self.running:
            try:
                current_text = pyperclip.paste()
                
                # 检查是否有新内容
                if (current_text != self.last_clipboard_text and 
                    current_text.strip() and 
                    len(current_text.strip()) > 2):
                    
                    self.last_clipboard_text = current_text
                    
                    # 检查是否需要翻译
                    if self._should_translate_clipboard(current_text):
                        await self._translate_clipboard_text(current_text)
                
                await asyncio.sleep(0.5)  # 每0.5秒检查一次
                
            except Exception as e:
                logger.error(f"剪贴板监控错误: {e}")
                await asyncio.sleep(1)
    
    def _should_translate_clipboard(self, text: str) -> bool:
        """判断剪贴板内容是否需要翻译"""
        text = text.strip()
        
        # 基本过滤
        if len(text) < 3 or len(text) > 1000:
            return False
        
        # 检查是否包含中文
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return False
        
        # 检查是否为纯数字或特殊字符
        if text.replace(' ', '').replace('\n', '').replace('\t', '').isdigit():
            return False
        
        # 检查是否为 URL
        if text.startswith(('http://', 'https://', 'ftp://')):
            return False
        
        # 检查是否为文件路径
        if '\\' in text or text.startswith('/'):
            return False
        
        return True
    
    async def _translate_clipboard_text(self, text: str) -> None:
        """翻译剪贴板文本"""
        try:
            logger.info(f"翻译剪贴板内容: {text[:50]}...")
            
            # 发布翻译开始事件
            await event_bus.publish(Events.TRANSLATION_STARTED, {
                'text': text,
                'source': 'clipboard'
            })
            
            # 执行翻译
            result = await translation_service.translate(text)
            
            # 添加到历史记录
            self._add_to_history(text, result.result)
            
            # 发布翻译完成事件
            await event_bus.publish(Events.TRANSLATION_COMPLETED, {
                'text': text,
                'result': result.result,
                'provider': result.provider,
                'source': 'clipboard'
            })
            
            # 显示翻译结果
            await self._show_translation_result(text, result.result)
            
        except Exception as e:
            logger.error(f"剪贴板翻译失败: {e}")
            await event_bus.publish(Events.TRANSLATION_FAILED, {
                'text': text,
                'error': str(e),
                'source': 'clipboard'
            })
    
    async def translate_text(self, text: str) -> Dict[str, Any]:
        """手动翻译文本"""
        try:
            result = await translation_service.translate(text)
            
            # 添加到历史记录
            self._add_to_history(text, result.result)
            
            return {
                'success': True,
                'original': text,
                'translation': result.result,
                'provider': result.provider,
                'cached': result.cached,
                'duration': result.duration
            }
            
        except Exception as e:
            logger.error(f"手动翻译失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'original': text
            }
    
    def _add_to_history(self, original: str, translation: str) -> None:
        """添加到翻译历史"""
        history_item = {
            'original': original,
            'translation': translation,
            'timestamp': time.time()
        }
        
        self.translation_history.insert(0, history_item)
        
        # 限制历史记录数量
        if len(self.translation_history) > self.max_history:
            self.translation_history = self.translation_history[:self.max_history]
    
    async def _show_translation_result(self, original: str, translation: str) -> None:
        """显示翻译结果"""
        try:
            # 尝试使用系统通知
            await self._show_notification(original, translation)
        except Exception as e:
            logger.debug(f"通知显示失败: {e}")
            # 备用方案：输出到控制台
            print(f"\n🔤 翻译结果:")
            print(f"原文: {original}")
            print(f"译文: {translation}")
            print("-" * 50)
    
    async def _show_notification(self, original: str, translation: str) -> None:
        """显示系统通知"""
        try:
            # Windows 通知
            import win10toast
            toaster = win10toast.ToastNotifier()
            toaster.show_toast(
                "SteamPlus 翻译",
                f"{original[:30]}...\n→ {translation[:50]}...",
                duration=3,
                threaded=True
            )
        except ImportError:
            # 备用通知方案
            try:
                import subprocess
                subprocess.run([
                    'powershell', '-Command',
                    f'Add-Type -AssemblyName System.Windows.Forms; '
                    f'[System.Windows.Forms.MessageBox]::Show("{translation}", "翻译结果", "OK", "Information")'
                ], creationflags=subprocess.CREATE_NO_WINDOW)
            except:
                pass
    
    async def _create_translation_shortcuts(self) -> None:
        """创建翻译快捷方式"""
        # 这里可以创建桌面快捷方式或注册全局热键
        # 暂时跳过，因为需要额外的依赖
        pass
    
    def get_translation_history(self, limit: int = 20) -> list:
        """获取翻译历史"""
        return self.translation_history[:limit]
    
    def clear_history(self) -> None:
        """清空翻译历史"""
        self.translation_history.clear()
        logger.info("翻译历史已清空")
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'running': self.running,
            'clipboard_monitor': self.clipboard_monitor_task is not None and not self.clipboard_monitor_task.done(),
            'clipboard_available': CLIPBOARD_AVAILABLE,
            'history_count': len(self.translation_history),
            'providers': translation_service.get_available_providers()
        }


# 全局独立翻译服务实例
standalone_translate_service = StandaloneTranslateService()
