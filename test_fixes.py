#!/usr/bin/env python3
"""
测试修复的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))


def test_tray_icon():
    """测试托盘图标创建"""
    print("🔍 测试托盘图标创建...")
    
    try:
        from PySide6.QtWidgets import QApplication, QSystemTrayIcon
        from steamplus.ui.tray import SystemTrayIcon
        
        # 创建应用
        app = QApplication.instance()
        if not app:
            app = QApplication([])
        
        # 检查系统托盘支持
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("❌ 系统托盘不可用")
            return False
        
        # 创建托盘图标
        tray_icon = SystemTrayIcon()
        print("✅ 托盘图标创建成功")
        
        # 测试图标
        icon = tray_icon._create_icon()
        if icon.isNull():
            print("⚠️  图标为空，但没有崩溃")
        else:
            print("✅ 图标创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 托盘图标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_steam_connection():
    """测试 Steam 连接"""
    print("\n🔍 测试 Steam 连接...")
    
    try:
        from steamplus.service.machine import ProcessUtils
        from steamplus.service.steam import steam_connection
        
        # 测试端口检测
        debug_port = ProcessUtils.get_steam_debug_port()
        if debug_port:
            print(f"✅ 找到 Steam 调试端口: {debug_port}")
        else:
            print("⚠️  未找到 Steam 调试端口（这是正常的，如果 Steam 未运行）")
        
        # 测试 Steam 进程检测
        steam_proc = ProcessUtils.find_steam_process()
        if steam_proc:
            print(f"✅ 找到 Steam 进程: {steam_proc.name()}")
        else:
            print("⚠️  未找到 Steam 进程（这是正常的，如果 Steam 未运行）")
        
        return True
        
    except Exception as e:
        print(f"❌ Steam 连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_server():
    """测试 API 服务器"""
    print("\n🔍 测试 API 服务器...")
    
    try:
        from steamplus.api.server import app
        from fastapi.testclient import TestClient
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试状态端点
        response = client.get("/status")
        if response.status_code == 200:
            print("✅ API 状态端点正常")
            data = response.json()
            print(f"   版本: {data.get('version')}")
            print(f"   状态: {data.get('status')}")
        else:
            print(f"❌ API 状态端点失败: {response.status_code}")
            return False
        
        # 测试翻译提供商端点
        response = client.get("/translate/providers")
        if response.status_code == 200:
            print("✅ 翻译提供商端点正常")
            data = response.json()
            providers = data.get('providers', [])
            print(f"   可用提供商: {', '.join(providers) if providers else '无'}")
        else:
            print(f"❌ 翻译提供商端点失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API 服务器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_settings():
    """测试设置管理"""
    print("\n🔍 测试设置管理...")
    
    try:
        from steamplus.service.settings import get_settings, save_settings
        
        # 加载设置
        settings = get_settings()
        print("✅ 设置加载成功")
        print(f"   翻译引擎: {settings.translate.provider}")
        print(f"   目标语言: {settings.translate.target_lang}")
        print(f"   API 端口: {settings.general.api_port}")
        
        # 测试保存设置
        original_provider = settings.translate.provider
        settings.translate.provider = "test"
        save_settings(settings)
        
        # 恢复设置
        settings.translate.provider = original_provider
        save_settings(settings)
        
        print("✅ 设置保存和恢复成功")
        return True
        
    except Exception as e:
        print(f"❌ 设置管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 SteamPlus 修复验证测试")
    print("=" * 50)
    
    tests = [
        ("设置管理", test_settings),
        ("API 服务器", test_api_server),
        ("Steam 连接", test_steam_connection),
        ("托盘图标", test_tray_icon),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        print("\n💡 使用建议:")
        print("   - 如果要使用 Steam 功能，请先启动 Steam 并添加调试参数")
        print("   - 运行 'python start_fixed.py --steam-help' 查看 Steam 设置说明")
        print("   - 运行 'python start_fixed.py --api-only' 启动仅 API 模式")
    else:
        print("⚠️  部分测试失败，但核心功能应该可用")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
