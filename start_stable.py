#!/usr/bin/env python3
"""
SteamPlus 稳定版启动脚本

专注于稳定性和可靠性的启动方式。
"""

import sys
import os
import asyncio
import signal
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class SteamPlusApp:
    """SteamPlus 应用主类"""
    
    def __init__(self):
        self.running = False
        self.services = {}
        self.cleanup_tasks = []
    
    async def initialize(self):
        """初始化应用"""
        print("🎮 SteamPlus 稳定版启动")
        print("=" * 50)
        
        try:
            # 1. 初始化事件系统
            print("📋 初始化事件系统...")
            from steamplus.service.events import event_bus
            await event_bus.initialize()
            self.services['events'] = event_bus
            
            # 2. 智能连接 Steam
            print("🔍 智能连接 Steam...")
            from steamplus.service.steam import steam_connection
            
            # 设置更长的超时时间
            steam_connected = await self._connect_steam_with_retry(steam_connection)
            self.services['steam'] = steam_connection
            
            if steam_connected:
                mode = steam_connection._connection_mode
                if mode == "cdp":
                    print("✅ Steam CDP 连接成功！")
                elif mode == "browser":
                    print("✅ 独立浏览器模式启动！")
                elif mode == "standalone":
                    print("✅ 独立翻译模式启动！")
            else:
                print("⚠️  Steam 连接失败，启用独立模式")
            
            # 3. 启动翻译服务
            print("🔤 启动翻译服务...")
            from steamplus.service.standalone_translate import standalone_translate_service
            await standalone_translate_service.initialize()
            self.services['translate'] = standalone_translate_service
            
            # 4. 启动 API 服务器
            print("🌐 启动 API 服务器...")
            api_task = await self._start_api_server()
            if api_task:
                self.cleanup_tasks.append(api_task)
            
            # 5. 尝试启动系统托盘
            tray_started = await self._start_system_tray()
            
            print("\n🎉 SteamPlus 启动成功！")
            print("=" * 50)
            
            # 显示功能状态
            await self._show_status()
            
            self.running = True
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _connect_steam_with_retry(self, steam_connection, max_retries=2):
        """带重试的 Steam 连接"""
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"🔄 重试连接 Steam (第 {attempt + 1} 次)...")
                
                success = await steam_connection.connect()
                if success:
                    return True
                    
                if attempt < max_retries - 1:
                    print("⏳ 等待 10 秒后重试...")
                    await asyncio.sleep(10)
                    
            except Exception as e:
                print(f"⚠️  连接尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
        
        return False
    
    async def _start_api_server(self):
        """启动 API 服务器"""
        try:
            import uvicorn
            from steamplus.api.server import app as api_app
            from steamplus.service.settings import get_settings
            
            settings = get_settings()
            port = settings.general.api_port
            
            config = uvicorn.Config(
                app=api_app,
                host="127.0.0.1",
                port=port,
                log_level="warning"
            )
            server = uvicorn.Server(config)
            
            # 在后台启动服务器
            api_task = asyncio.create_task(server.serve())
            
            print(f"📡 API 服务: http://localhost:{port}")
            print(f"📖 API 文档: http://localhost:{port}/docs")
            
            return api_task
            
        except Exception as e:
            print(f"⚠️  API 服务器启动失败: {e}")
            return None
    
    async def _start_system_tray(self):
        """启动系统托盘"""
        try:
            from steamplus.ui import tray_app
            
            if tray_app.start():
                print("✅ 系统托盘已启动")
                return True
            else:
                print("⚠️  系统托盘启动失败")
                return False
                
        except ImportError:
            print("⚠️  系统托盘不可用 (PySide6 未安装)")
            return False
        except Exception as e:
            print(f"⚠️  系统托盘启动失败: {e}")
            return False
    
    async def _show_status(self):
        """显示状态信息"""
        print("\n💡 可用功能:")
        
        # Steam 连接状态
        steam_service = self.services.get('steam')
        if steam_service and steam_service.is_connected:
            mode = steam_service._connection_mode
            if mode == "cdp":
                print("   ✅ Steam 页面翻译 - 在 Steam 中选中文本即可翻译")
            elif mode == "browser":
                print("   ✅ 浏览器翻译 - 在打开的浏览器中访问 Steam")
        
        # 翻译服务状态
        translate_service = self.services.get('translate')
        if translate_service and translate_service.running:
            print("   ✅ 剪贴板翻译 - 复制文本自动翻译")
            print("   ✅ API 翻译 - 通过 HTTP API 调用翻译")
        
        print("\n🔧 使用说明:")
        print("   - 复制英文文本到剪贴板，会自动翻译并显示通知")
        print("   - 访问 API 文档页面可以测试翻译功能")
        print("   - 查看系统托盘图标进行控制")
        print("   - 按 Ctrl+C 退出程序")
    
    async def run(self):
        """运行应用主循环"""
        if not self.running:
            return
        
        try:
            # 设置信号处理
            def signal_handler(signum, frame):
                print(f"\n⏹️  收到信号 {signum}，准备退出...")
                self.running = False
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # 主循环
            while self.running:
                await asyncio.sleep(1)
                
                # 检查服务状态
                if not await self._check_services():
                    print("⚠️  检测到服务异常，准备退出...")
                    break
                    
        except KeyboardInterrupt:
            print("\n⏹️  收到键盘中断...")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            await self.cleanup()
    
    async def _check_services(self):
        """检查服务状态"""
        try:
            # 检查事件系统
            events = self.services.get('events')
            if events and not events.is_running:
                print("⚠️  事件系统已停止")
                return False
            
            # 检查翻译服务
            translate = self.services.get('translate')
            if translate and not translate.running:
                print("⚠️  翻译服务已停止")
                return False
            
            return True
            
        except Exception as e:
            print(f"⚠️  服务检查失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        print("🧹 清理资源...")
        
        self.running = False
        
        # 取消后台任务
        for task in self.cleanup_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # 清理服务
        for name, service in self.services.items():
            try:
                if hasattr(service, 'cleanup'):
                    await service.cleanup()
                elif hasattr(service, 'disconnect'):
                    await service.disconnect()
                elif hasattr(service, 'stop'):
                    service.stop()
            except Exception as e:
                print(f"⚠️  清理 {name} 服务时出错: {e}")
        
        print("👋 再见！")


async def main():
    """主函数"""
    app = SteamPlusApp()
    
    try:
        if await app.initialize():
            await app.run()
        else:
            print("❌ 应用初始化失败")
            return 1
    except Exception as e:
        print(f"💥 应用异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        await app.cleanup()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 启动失败: {e}")
        sys.exit(1)
