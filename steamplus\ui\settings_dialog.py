"""
设置对话框

基于 PySide6 的设置界面，提供各种配置选项。
"""

from typing import Dict, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QGroupBox, QFormLayout, QMessageBox, QTextEdit,
    QSlider, QProgressBar
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont
from loguru import logger

from ..service.settings import get_settings, save_settings, Settings
from ..service.translate import translation_service


class GeneralTab(QWidget):
    """通用设置标签页"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 启动设置组
        startup_group = QGroupBox("启动设置")
        startup_layout = QFormLayout()
        
        self.autostart_cb = QCheckBox("开机自启动")
        startup_layout.addRow(self.autostart_cb)
        
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["stable", "beta", "dev"])
        startup_layout.addRow("更新通道:", self.channel_combo)
        
        startup_group.setLayout(startup_layout)
        layout.addWidget(startup_group)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout()
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["zh-CN", "en-US", "ja-JP"])
        ui_layout.addRow("界面语言:", self.language_combo)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        ui_layout.addRow("日志级别:", self.log_level_combo)
        
        ui_group.setLayout(ui_layout)
        layout.addWidget(ui_group)
        
        # API 设置组
        api_group = QGroupBox("API 设置")
        api_layout = QFormLayout()
        
        self.api_port_spin = QSpinBox()
        self.api_port_spin.setRange(1024, 65535)
        self.api_port_spin.setValue(50055)
        api_layout.addRow("API 端口:", self.api_port_spin)
        
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self, settings: Settings):
        """加载设置"""
        self.autostart_cb.setChecked(settings.general.autostart)
        self.channel_combo.setCurrentText(settings.general.channel)
        self.language_combo.setCurrentText(settings.general.language)
        self.log_level_combo.setCurrentText(settings.general.log_level)
        self.api_port_spin.setValue(settings.general.api_port)
    
    def save_settings(self, settings: Settings):
        """保存设置"""
        settings.general.autostart = self.autostart_cb.isChecked()
        settings.general.channel = self.channel_combo.currentText()
        settings.general.language = self.language_combo.currentText()
        settings.general.log_level = self.log_level_combo.currentText()
        settings.general.api_port = self.api_port_spin.value()


class TranslateTab(QWidget):
    """翻译设置标签页"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 翻译引擎设置组
        engine_group = QGroupBox("翻译引擎")
        engine_layout = QFormLayout()
        
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["bing", "google", "deepl", "niutrans"])
        engine_layout.addRow("翻译提供商:", self.provider_combo)
        
        self.auto_detect_cb = QCheckBox("自动检测语言")
        engine_layout.addRow(self.auto_detect_cb)
        
        self.source_lang_combo = QComboBox()
        self.source_lang_combo.addItems(["auto", "en", "zh-CN", "ja", "ko", "fr", "de", "es"])
        engine_layout.addRow("源语言:", self.source_lang_combo)
        
        self.target_lang_combo = QComboBox()
        self.target_lang_combo.addItems(["zh-CN", "en", "ja", "ko", "fr", "de", "es"])
        engine_layout.addRow("目标语言:", self.target_lang_combo)
        
        engine_group.setLayout(engine_layout)
        layout.addWidget(engine_group)
        
        # 显示设置组
        display_group = QGroupBox("显示设置")
        display_layout = QFormLayout()
        
        self.show_original_cb = QCheckBox("显示原文")
        display_layout.addRow(self.show_original_cb)
        
        self.delay_spin = QDoubleSpinBox()
        self.delay_spin.setRange(0.1, 5.0)
        self.delay_spin.setSingleStep(0.1)
        self.delay_spin.setSuffix(" 秒")
        display_layout.addRow("翻译延迟:", self.delay_spin)
        
        display_group.setLayout(display_layout)
        layout.addWidget(display_group)
        
        # 测试区域
        test_group = QGroupBox("翻译测试")
        test_layout = QVBoxLayout()
        
        self.test_input = QLineEdit()
        self.test_input.setPlaceholderText("输入要测试的文本...")
        test_layout.addWidget(self.test_input)
        
        test_btn_layout = QHBoxLayout()
        self.test_btn = QPushButton("测试翻译")
        self.test_btn.clicked.connect(self.test_translation)
        test_btn_layout.addWidget(self.test_btn)
        test_btn_layout.addStretch()
        test_layout.addLayout(test_btn_layout)
        
        self.test_result = QTextEdit()
        self.test_result.setMaximumHeight(100)
        self.test_result.setReadOnly(True)
        test_layout.addWidget(self.test_result)
        
        test_group.setLayout(test_layout)
        layout.addWidget(test_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self, settings: Settings):
        """加载设置"""
        self.provider_combo.setCurrentText(settings.translate.provider)
        self.auto_detect_cb.setChecked(settings.translate.auto_detect)
        self.source_lang_combo.setCurrentText(settings.translate.source_lang)
        self.target_lang_combo.setCurrentText(settings.translate.target_lang)
        self.show_original_cb.setChecked(settings.translate.show_original)
        self.delay_spin.setValue(settings.translate.translation_delay)
    
    def save_settings(self, settings: Settings):
        """保存设置"""
        settings.translate.provider = self.provider_combo.currentText()
        settings.translate.auto_detect = self.auto_detect_cb.isChecked()
        settings.translate.source_lang = self.source_lang_combo.currentText()
        settings.translate.target_lang = self.target_lang_combo.currentText()
        settings.translate.show_original = self.show_original_cb.isChecked()
        settings.translate.translation_delay = self.delay_spin.value()
    
    def test_translation(self):
        """测试翻译功能"""
        text = self.test_input.text().strip()
        if not text:
            self.test_result.setText("请输入要测试的文本")
            return
        
        self.test_btn.setEnabled(False)
        self.test_result.setText("翻译中...")
        
        # 这里应该调用翻译服务进行测试
        # 由于是在 UI 线程中，需要使用异步方式
        QTimer.singleShot(1000, lambda: self._show_test_result(text))
    
    def _show_test_result(self, text: str):
        """显示测试结果"""
        # 模拟翻译结果
        result = f"原文: {text}\n翻译: [模拟翻译结果] {text}"
        self.test_result.setText(result)
        self.test_btn.setEnabled(True)


class SteamTab(QWidget):
    """Steam 设置标签页"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout()
        
        self.auto_connect_cb = QCheckBox("自动连接 Steam")
        connection_layout.addRow(self.auto_connect_cb)
        
        self.debug_port_spin = QSpinBox()
        self.debug_port_spin.setRange(0, 65535)
        self.debug_port_spin.setSpecialValueText("自动检测")
        connection_layout.addRow("调试端口:", self.debug_port_spin)
        
        connection_group.setLayout(connection_layout)
        layout.addWidget(connection_group)
        
        # 注入设置组
        inject_group = QGroupBox("页面注入")
        inject_layout = QVBoxLayout()
        
        self.inject_store_cb = QCheckBox("商店页面")
        self.inject_community_cb = QCheckBox("社区页面")
        self.inject_library_cb = QCheckBox("库页面")
        
        inject_layout.addWidget(self.inject_store_cb)
        inject_layout.addWidget(self.inject_community_cb)
        inject_layout.addWidget(self.inject_library_cb)
        
        inject_group.setLayout(inject_layout)
        layout.addWidget(inject_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self, settings: Settings):
        """加载设置"""
        self.auto_connect_cb.setChecked(settings.steam.auto_connect)
        self.debug_port_spin.setValue(settings.steam.debug_port or 0)
        
        inject_pages = settings.steam.inject_pages
        self.inject_store_cb.setChecked("store" in inject_pages)
        self.inject_community_cb.setChecked("community" in inject_pages)
        self.inject_library_cb.setChecked("library" in inject_pages)
    
    def save_settings(self, settings: Settings):
        """保存设置"""
        settings.steam.auto_connect = self.auto_connect_cb.isChecked()
        settings.steam.debug_port = self.debug_port_spin.value() if self.debug_port_spin.value() > 0 else None
        
        inject_pages = []
        if self.inject_store_cb.isChecked():
            inject_pages.append("store")
        if self.inject_community_cb.isChecked():
            inject_pages.append("community")
        if self.inject_library_cb.isChecked():
            inject_pages.append("library")
        
        settings.steam.inject_pages = inject_pages


class UITab(QWidget):
    """界面设置标签页"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 通知设置组
        notification_group = QGroupBox("通知设置")
        notification_layout = QFormLayout()
        
        self.show_notifications_cb = QCheckBox("显示通知")
        notification_layout.addRow(self.show_notifications_cb)
        
        notification_group.setLayout(notification_layout)
        layout.addWidget(notification_group)
        
        # 窗口设置组
        window_group = QGroupBox("窗口设置")
        window_layout = QFormLayout()
        
        self.minimize_to_tray_cb = QCheckBox("最小化到托盘")
        window_layout.addRow(self.minimize_to_tray_cb)
        
        self.close_to_tray_cb = QCheckBox("关闭到托盘")
        window_layout.addRow(self.close_to_tray_cb)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["system", "light", "dark"])
        window_layout.addRow("主题:", self.theme_combo)
        
        window_group.setLayout(window_layout)
        layout.addWidget(window_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self, settings: Settings):
        """加载设置"""
        self.show_notifications_cb.setChecked(settings.ui.show_notifications)
        self.minimize_to_tray_cb.setChecked(settings.ui.minimize_to_tray)
        self.close_to_tray_cb.setChecked(settings.ui.close_to_tray)
        self.theme_combo.setCurrentText(settings.ui.theme)
    
    def save_settings(self, settings: Settings):
        """保存设置"""
        settings.ui.show_notifications = self.show_notifications_cb.isChecked()
        settings.ui.minimize_to_tray = self.minimize_to_tray_cb.isChecked()
        settings.ui.close_to_tray = self.close_to_tray_cb.isChecked()
        settings.ui.theme = self.theme_combo.currentText()


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SteamPlus 设置")
        self.setFixedSize(500, 600)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        self.general_tab = GeneralTab()
        self.translate_tab = TranslateTab()
        self.steam_tab = SteamTab()
        self.ui_tab = UITab()
        
        self.tab_widget.addTab(self.general_tab, "通用")
        self.tab_widget.addTab(self.translate_tab, "翻译")
        self.tab_widget.addTab(self.steam_tab, "Steam")
        self.tab_widget.addTab(self.ui_tab, "界面")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_settings)
        self.save_btn.setDefault(True)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_current_settings(self):
        """加载当前设置"""
        try:
            settings = get_settings()
            
            self.general_tab.load_settings(settings)
            self.translate_tab.load_settings(settings)
            self.steam_tab.load_settings(settings)
            self.ui_tab.load_settings(settings)
            
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
            QMessageBox.warning(self, "警告", f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = get_settings()
            
            self.general_tab.save_settings(settings)
            self.translate_tab.save_settings(settings)
            self.steam_tab.save_settings(settings)
            self.ui_tab.save_settings(settings)
            
            save_settings(settings)
            
            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()
            
        except Exception as e:
            logger.error(f"Failed to save settings: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
    
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有设置为默认值吗？\n\n此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 重置为默认设置
                default_settings = Settings()
                save_settings(default_settings)
                
                # 重新加载界面
                self.load_current_settings()
                
                QMessageBox.information(self, "成功", "设置已重置为默认值")
                
            except Exception as e:
                logger.error(f"Failed to reset settings: {e}")
                QMessageBox.critical(self, "错误", f"重置设置失败: {e}")
