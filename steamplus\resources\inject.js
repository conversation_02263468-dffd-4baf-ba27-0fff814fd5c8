/**
 * SteamPlus 翻译注入脚本
 * 
 * 在 Steam 页面中注入翻译功能，支持划词翻译和悬停翻译。
 */

(function() {
    'use strict';
    
    // 避免重复注入
    if (window.steamplus_injected) {
        return;
    }
    window.steamplus_injected = true;
    
    console.log('[SteamPlus] Translation script injected');
    
    // 配置
    const config = {
        enabled: true,
        showOriginal: true,
        translationDelay: 500,
        minTextLength: 3,
        maxTextLength: 1000,
        excludeSelectors: [
            'input', 'textarea', 'select', 'button',
            '.steamplus-translation', '.steamplus-tooltip'
        ]
    };
    
    // 状态管理
    let isTranslating = false;
    let currentTooltip = null;
    let translationCache = new Map();
    let selectionTimeout = null;
    
    // 创建翻译提示框
    function createTooltip() {
        const tooltip = document.createElement('div');
        tooltip.className = 'steamplus-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        `;
        document.body.appendChild(tooltip);
        return tooltip;
    }
    
    // 显示翻译结果
    function showTranslation(text, result, x, y) {
        hideTooltip();
        
        currentTooltip = createTooltip();
        
        let content = result;
        if (config.showOriginal && text !== result) {
            content = `${result}<br><small style="opacity: 0.7;">原文: ${text}</small>`;
        }
        
        currentTooltip.innerHTML = content;
        
        // 定位提示框
        const rect = currentTooltip.getBoundingClientRect();
        let left = x;
        let top = y - rect.height - 10;
        
        // 边界检查
        if (left + rect.width > window.innerWidth) {
            left = window.innerWidth - rect.width - 10;
        }
        if (left < 10) {
            left = 10;
        }
        if (top < 10) {
            top = y + 20;
        }
        
        currentTooltip.style.left = left + 'px';
        currentTooltip.style.top = top + 'px';
        currentTooltip.style.opacity = '1';
        
        // 自动隐藏
        setTimeout(() => {
            hideTooltip();
        }, 5000);
    }
    
    // 隐藏提示框
    function hideTooltip() {
        if (currentTooltip) {
            currentTooltip.remove();
            currentTooltip = null;
        }
    }
    
    // 检查文本是否需要翻译
    function shouldTranslate(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        text = text.trim();
        
        if (text.length < config.minTextLength || text.length > config.maxTextLength) {
            return false;
        }
        
        // 检查是否包含中文
        if (/[\u4e00-\u9fff]/.test(text)) {
            return false;
        }
        
        // 检查是否为纯数字或特殊字符
        if (/^[\d\s\-_.,!@#$%^&*()+=\[\]{}|;:'"<>?/\\`~]*$/.test(text)) {
            return false;
        }
        
        return true;
    }
    
    // 翻译文本
    async function translateText(text) {
        if (!shouldTranslate(text)) {
            return null;
        }
        
        // 检查缓存
        if (translationCache.has(text)) {
            return translationCache.get(text);
        }
        
        if (isTranslating) {
            return null;
        }
        
        isTranslating = true;
        
        try {
            // 调用翻译接口
            const result = await window.steamplus_translate({
                text: text,
                source: 'auto',
                target: 'zh-CN'
            });
            
            if (result && result.result) {
                translationCache.set(text, result.result);
                return result.result;
            } else if (result && result.error) {
                console.error('[SteamPlus] Translation error:', result.error);
            }
            
            return null;
            
        } catch (error) {
            console.error('[SteamPlus] Translation failed:', error);
            return null;
        } finally {
            isTranslating = false;
        }
    }
    
    // 处理文本选择
    function handleTextSelection() {
        if (!config.enabled) {
            return;
        }
        
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) {
            hideTooltip();
            return;
        }
        
        const text = selection.toString().trim();
        if (!text) {
            hideTooltip();
            return;
        }
        
        // 清除之前的定时器
        if (selectionTimeout) {
            clearTimeout(selectionTimeout);
        }
        
        // 延迟翻译
        selectionTimeout = setTimeout(async () => {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            
            if (rect.width === 0 || rect.height === 0) {
                return;
            }
            
            const result = await translateText(text);
            if (result) {
                const x = rect.left + rect.width / 2;
                const y = rect.top + window.scrollY;
                showTranslation(text, result, x, y);
            }
        }, config.translationDelay);
    }
    
    // 处理鼠标悬停
    function handleMouseOver(event) {
        if (!config.enabled || isTranslating) {
            return;
        }
        
        const target = event.target;
        
        // 检查是否为排除的元素
        for (const selector of config.excludeSelectors) {
            if (target.matches && target.matches(selector)) {
                return;
            }
        }
        
        // 获取元素文本
        let text = '';
        if (target.textContent) {
            text = target.textContent.trim();
        }
        
        if (!shouldTranslate(text)) {
            return;
        }
        
        // 避免翻译过长的文本
        if (text.length > 200) {
            return;
        }
        
        // 延迟翻译
        setTimeout(async () => {
            if (target !== document.elementFromPoint(event.clientX, event.clientY)) {
                return; // 鼠标已移开
            }
            
            const result = await translateText(text);
            if (result) {
                showTranslation(text, result, event.clientX, event.clientY);
            }
        }, config.translationDelay * 2);
    }
    
    // 事件监听器
    document.addEventListener('mouseup', handleTextSelection);
    document.addEventListener('selectionchange', () => {
        setTimeout(handleTextSelection, 100);
    });
    
    // 鼠标悬停翻译（可选功能）
    // document.addEventListener('mouseover', handleMouseOver);
    
    // 点击其他地方隐藏提示框
    document.addEventListener('click', (event) => {
        if (currentTooltip && !currentTooltip.contains(event.target)) {
            hideTooltip();
        }
    });
    
    // 键盘快捷键
    document.addEventListener('keydown', (event) => {
        // Ctrl+Shift+T 切换翻译功能
        if (event.ctrlKey && event.shiftKey && event.key === 'T') {
            config.enabled = !config.enabled;
            console.log(`[SteamPlus] Translation ${config.enabled ? 'enabled' : 'disabled'}`);
            
            if (!config.enabled) {
                hideTooltip();
            }
            
            event.preventDefault();
        }
        
        // ESC 隐藏提示框
        if (event.key === 'Escape') {
            hideTooltip();
        }
    });
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        hideTooltip();
        if (selectionTimeout) {
            clearTimeout(selectionTimeout);
        }
    });
    
    // 暴露配置接口
    window.steamplus_config = config;
    
    console.log('[SteamPlus] Translation script ready');
    
})();
