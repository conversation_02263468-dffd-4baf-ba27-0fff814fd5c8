"""
自动更新模块

基于 GitHub Releases 的自动更新功能。
"""

import asyncio
import json
import os
import tempfile
from pathlib import Path
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from packaging import version

import aiohttp
import aiofiles
from loguru import logger

from .events import event_bus, Events
from .settings import get_settings


@dataclass
class UpdateInfo:
    """更新信息"""
    version: str
    download_url: str
    changelog: str
    size: int
    published_at: str
    prerelease: bool = False


class UpdaterService:
    """更新服务"""
    
    def __init__(self):
        self.current_version = "0.1.0"
        self.github_repo = "steamplus/steamplus"
        self.update_check_interval = 3600  # 1小时检查一次
        self.session: Optional[aiohttp.ClientSession] = None
        self._check_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def initialize(self) -> None:
        """初始化更新服务"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': f'SteamPlus/{self.current_version}'}
        )
        
        # 启动定期检查任务
        settings = get_settings()
        if settings.general.channel in ["stable", "beta"]:
            self._running = True
            self._check_task = asyncio.create_task(self._periodic_check())
        
        logger.info("Updater service initialized")
    
    async def cleanup(self) -> None:
        """清理更新服务"""
        self._running = False
        
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
            self.session = None
        
        logger.info("Updater service cleaned up")
    
    async def check_for_updates(self) -> Optional[UpdateInfo]:
        """检查更新"""
        try:
            settings = get_settings()
            channel = settings.general.channel
            
            # 获取最新版本信息
            if channel == "stable":
                update_info = await self._get_latest_release()
            elif channel == "beta":
                update_info = await self._get_latest_prerelease()
            else:
                # dev 通道不检查更新
                return None
            
            if not update_info:
                return None
            
            # 比较版本
            if version.parse(update_info.version) > version.parse(self.current_version):
                logger.info(f"New version available: {update_info.version}")
                return update_info
            else:
                logger.debug("No updates available")
                return None
                
        except Exception as e:
            logger.error(f"Failed to check for updates: {e}")
            return None
    
    async def _get_latest_release(self) -> Optional[UpdateInfo]:
        """获取最新稳定版本"""
        try:
            url = f"https://api.github.com/repos/{self.github_repo}/releases/latest"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_release_data(data)
                else:
                    logger.warning(f"Failed to get latest release: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting latest release: {e}")
            return None
    
    async def _get_latest_prerelease(self) -> Optional[UpdateInfo]:
        """获取最新预发布版本"""
        try:
            url = f"https://api.github.com/repos/{self.github_repo}/releases"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    releases = await response.json()
                    
                    # 查找最新的预发布版本
                    for release in releases:
                        if release.get('prerelease', False):
                            return self._parse_release_data(release)
                    
                    # 如果没有预发布版本，返回最新稳定版本
                    if releases:
                        return self._parse_release_data(releases[0])
                    
                    return None
                else:
                    logger.warning(f"Failed to get releases: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting prerelease: {e}")
            return None
    
    def _parse_release_data(self, data: Dict) -> Optional[UpdateInfo]:
        """解析发布数据"""
        try:
            # 查找 Windows 可执行文件
            download_url = None
            file_size = 0
            
            for asset in data.get('assets', []):
                name = asset.get('name', '').lower()
                if name.endswith('.exe') and 'windows' in name:
                    download_url = asset.get('browser_download_url')
                    file_size = asset.get('size', 0)
                    break
            
            if not download_url:
                logger.warning("No Windows executable found in release")
                return None
            
            return UpdateInfo(
                version=data.get('tag_name', '').lstrip('v'),
                download_url=download_url,
                changelog=data.get('body', ''),
                size=file_size,
                published_at=data.get('published_at', ''),
                prerelease=data.get('prerelease', False)
            )
            
        except Exception as e:
            logger.error(f"Error parsing release data: {e}")
            return None
    
    async def download_update(self, update_info: UpdateInfo, progress_callback=None) -> Optional[Path]:
        """下载更新"""
        try:
            # 创建临时文件
            temp_dir = Path(tempfile.gettempdir()) / "steamplus_update"
            temp_dir.mkdir(exist_ok=True)
            
            filename = f"steamplus_{update_info.version}.exe"
            temp_file = temp_dir / filename
            
            logger.info(f"Downloading update to: {temp_file}")
            
            async with self.session.get(update_info.download_url) as response:
                if response.status != 200:
                    raise Exception(f"Download failed: {response.status}")
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                async with aiofiles.open(temp_file, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                        downloaded += len(chunk)
                        
                        if progress_callback and total_size > 0:
                            progress = (downloaded / total_size) * 100
                            await progress_callback(progress)
            
            logger.info(f"Update downloaded successfully: {temp_file}")
            return temp_file
            
        except Exception as e:
            logger.error(f"Failed to download update: {e}")
            return None
    
    async def install_update(self, update_file: Path) -> bool:
        """安装更新"""
        try:
            if not update_file.exists():
                raise Exception("Update file not found")
            
            # 获取当前可执行文件路径
            current_exe = Path(os.sys.executable)
            if not current_exe.exists():
                current_exe = Path(__file__).parent.parent.parent / "steamplus.exe"
            
            # 创建更新脚本
            update_script = self._create_update_script(update_file, current_exe)
            
            # 执行更新脚本
            import subprocess
            subprocess.Popen([update_script], shell=True)
            
            logger.info("Update installation initiated")
            
            # 发布更新事件
            await event_bus.publish("update_installing", {
                'update_file': str(update_file),
                'current_exe': str(current_exe)
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to install update: {e}")
            return False
    
    def _create_update_script(self, update_file: Path, current_exe: Path) -> Path:
        """创建更新脚本"""
        script_content = f"""
@echo off
echo Updating SteamPlus...
timeout /t 3 /nobreak > nul

echo Stopping current process...
taskkill /f /im "{current_exe.name}" > nul 2>&1

echo Backing up current version...
if exist "{current_exe}.backup" del "{current_exe}.backup"
if exist "{current_exe}" ren "{current_exe}" "{current_exe.name}.backup"

echo Installing new version...
copy "{update_file}" "{current_exe}"

echo Starting new version...
start "" "{current_exe}"

echo Cleaning up...
del "{update_file}"
del "%~f0"
"""
        
        script_path = update_file.parent / "update.bat"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    async def _periodic_check(self) -> None:
        """定期检查更新"""
        logger.info("Periodic update check started")
        
        while self._running:
            try:
                update_info = await self.check_for_updates()
                if update_info:
                    # 发布更新可用事件
                    await event_bus.publish("update_available", {
                        'version': update_info.version,
                        'changelog': update_info.changelog,
                        'size': update_info.size,
                        'prerelease': update_info.prerelease
                    })
                
                # 等待下次检查
                await asyncio.sleep(self.update_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic update check: {e}")
                await asyncio.sleep(300)  # 出错时等待5分钟再试
        
        logger.info("Periodic update check stopped")


# 全局更新服务实例
updater_service = UpdaterService()
