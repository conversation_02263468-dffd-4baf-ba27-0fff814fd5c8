"""
翻译服务模块

统一的翻译适配层，支持多种翻译引擎。
"""

import asyncio
import hashlib
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import aiohttp
from loguru import logger

from .events import event_bus, Events
from .settings import get_settings, get_secure_setting


class TranslationProvider(Enum):
    """翻译提供商枚举"""
    BING = "bing"
    DEEPL = "deepl"
    GOOGLE = "google"
    NIUTRANS = "niutrans"


@dataclass
class TranslationResult:
    """翻译结果"""
    text: str
    result: str
    source_lang: str
    target_lang: str
    provider: str
    confidence: float = 0.0
    cached: bool = False
    duration: float = 0.0


class BaseTranslator(ABC):
    """翻译器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        self.rate_limit = 10  # 每秒请求数限制
        self.last_request_time = 0
        self.request_count = 0
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self) -> None:
        """初始化翻译器"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            headers={'User-Agent': 'SteamPlus/0.1.0'}
        )
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None
    
    @abstractmethod
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh-CN"
    ) -> TranslationResult:
        """翻译文本"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查翻译器是否可用"""
        pass
    
    async def _rate_limit_check(self) -> None:
        """速率限制检查"""
        current_time = time.time()
        
        # 重置计数器（每秒）
        if current_time - self.last_request_time >= 1.0:
            self.request_count = 0
            self.last_request_time = current_time
        
        # 检查是否超过限制
        if self.request_count >= self.rate_limit:
            sleep_time = 1.0 - (current_time - self.last_request_time)
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.last_request_time = time.time()
        
        self.request_count += 1


class BingTranslator(BaseTranslator):
    """Bing 翻译器"""
    
    def __init__(self):
        super().__init__("bing")
        self.base_url = "https://api.cognitive.microsofttranslator.com"
        self.api_key = None
    
    async def initialize(self) -> None:
        await super().initialize()
        self.api_key = get_secure_setting("bing_api_key")
    
    def is_available(self) -> bool:
        return self.enabled and self.api_key is not None
    
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh-CN"
    ) -> TranslationResult:
        await self._rate_limit_check()
        
        start_time = time.time()
        
        try:
            # 如果没有 API 密钥，使用免费接口
            if not self.api_key:
                return await self._translate_free(text, source_lang, target_lang)
            
            # 使用官方 API
            headers = {
                'Ocp-Apim-Subscription-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            data = [{
                'text': text
            }]
            
            params = {
                'api-version': '3.0',
                'to': target_lang
            }
            
            if source_lang != "auto":
                params['from'] = source_lang
            
            async with self.session.post(
                f"{self.base_url}/translate",
                headers=headers,
                json=data,
                params=params
            ) as response:
                if response.status == 200:
                    result_data = await response.json()
                    translation = result_data[0]['translations'][0]
                    
                    return TranslationResult(
                        text=text,
                        result=translation['text'],
                        source_lang=result_data[0].get('detectedLanguage', {}).get('language', source_lang),
                        target_lang=target_lang,
                        provider=self.name,
                        confidence=result_data[0].get('detectedLanguage', {}).get('score', 0.0),
                        duration=time.time() - start_time
                    )
                else:
                    raise Exception(f"Bing API error: {response.status}")
                    
        except Exception as e:
            logger.error(f"Bing translation failed: {e}")
            raise
    
    async def _translate_free(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str
    ) -> TranslationResult:
        """使用免费的 Bing 翻译接口"""
        try:
            # 这里实现免费的 Bing 翻译逻辑
            # 注意：这只是示例，实际使用时需要实现具体的免费接口调用
            
            url = "https://www.bing.com/ttranslatev3"
            
            data = {
                'text': text,
                'fromLang': source_lang if source_lang != "auto" else "auto-detect",
                'toLang': target_lang
            }
            
            async with self.session.post(url, data=data) as response:
                if response.status == 200:
                    result_data = await response.json()
                    
                    # 解析结果（具体格式需要根据实际接口调整）
                    translation = result_data.get('translationResponse', text)
                    
                    return TranslationResult(
                        text=text,
                        result=translation,
                        source_lang=source_lang,
                        target_lang=target_lang,
                        provider=self.name,
                        duration=time.time() - time.time()
                    )
                else:
                    raise Exception(f"Free Bing API error: {response.status}")
                    
        except Exception as e:
            logger.error(f"Free Bing translation failed: {e}")
            # 返回原文作为后备
            return TranslationResult(
                text=text,
                result=f"[翻译失败] {text}",
                source_lang=source_lang,
                target_lang=target_lang,
                provider=self.name,
                duration=0.0
            )


class GoogleTranslator(BaseTranslator):
    """Google 翻译器"""
    
    def __init__(self):
        super().__init__("google")
        self.base_url = "https://translate.googleapis.com/translate_a/single"
    
    def is_available(self) -> bool:
        return self.enabled
    
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh-CN"
    ) -> TranslationResult:
        await self._rate_limit_check()
        
        start_time = time.time()
        
        try:
            params = {
                'client': 'gtx',
                'sl': source_lang,
                'tl': target_lang,
                'dt': 't',
                'q': text
            }
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    result_data = await response.json()
                    
                    # 解析 Google 翻译结果
                    translation = ""
                    if result_data and len(result_data) > 0:
                        for item in result_data[0]:
                            if item and len(item) > 0:
                                translation += item[0]
                    
                    detected_lang = source_lang
                    if len(result_data) > 2 and result_data[2]:
                        detected_lang = result_data[2]
                    
                    return TranslationResult(
                        text=text,
                        result=translation or text,
                        source_lang=detected_lang,
                        target_lang=target_lang,
                        provider=self.name,
                        duration=time.time() - start_time
                    )
                else:
                    raise Exception(f"Google API error: {response.status}")
                    
        except Exception as e:
            logger.error(f"Google translation failed: {e}")
            raise


class TranslationService:
    """翻译服务管理器"""
    
    def __init__(self):
        self.translators: Dict[str, BaseTranslator] = {}
        self.cache: Dict[str, TranslationResult] = {}
        self.cache_size_limit = 1000
        self.default_provider = "bing"
        
        # 初始化翻译器
        self.translators["bing"] = BingTranslator()
        self.translators["google"] = GoogleTranslator()
    
    async def initialize(self) -> None:
        """初始化翻译服务"""
        for translator in self.translators.values():
            try:
                await translator.initialize()
                logger.info(f"Translator {translator.name} initialized")
            except Exception as e:
                logger.error(f"Failed to initialize translator {translator.name}: {e}")
    
    async def cleanup(self) -> None:
        """清理翻译服务"""
        for translator in self.translators.values():
            try:
                await translator.cleanup()
            except Exception as e:
                logger.error(f"Failed to cleanup translator {translator.name}: {e}")
        
        self.cache.clear()
    
    def _get_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """生成缓存键"""
        content = f"{text}|{source_lang}|{target_lang}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh-CN",
        provider: Optional[str] = None
    ) -> TranslationResult:
        """翻译文本"""
        if not text or not text.strip():
            return TranslationResult(
                text=text,
                result=text,
                source_lang=source_lang,
                target_lang=target_lang,
                provider="none"
            )
        
        # 检查缓存
        cache_key = self._get_cache_key(text, source_lang, target_lang)
        if cache_key in self.cache:
            result = self.cache[cache_key]
            result.cached = True
            return result
        
        # 选择翻译器
        if not provider:
            settings = get_settings()
            provider = settings.translate.provider
        
        translator = self._get_available_translator(provider)
        if not translator:
            raise Exception("No available translator")
        
        try:
            # 执行翻译
            result = await translator.translate(text, source_lang, target_lang)
            
            # 缓存结果
            self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Translation failed with {translator.name}: {e}")
            
            # 尝试备用翻译器
            for backup_name, backup_translator in self.translators.items():
                if backup_name != translator.name and backup_translator.is_available():
                    try:
                        result = await backup_translator.translate(text, source_lang, target_lang)
                        self._cache_result(cache_key, result)
                        return result
                    except Exception as backup_error:
                        logger.error(f"Backup translation failed with {backup_name}: {backup_error}")
            
            # 所有翻译器都失败，返回原文
            return TranslationResult(
                text=text,
                result=text,
                source_lang=source_lang,
                target_lang=target_lang,
                provider="fallback"
            )
    
    def _get_available_translator(self, provider: str) -> Optional[BaseTranslator]:
        """获取可用的翻译器"""
        if provider in self.translators and self.translators[provider].is_available():
            return self.translators[provider]
        
        # 查找第一个可用的翻译器
        for translator in self.translators.values():
            if translator.is_available():
                return translator
        
        return None
    
    def _cache_result(self, key: str, result: TranslationResult) -> None:
        """缓存翻译结果"""
        if len(self.cache) >= self.cache_size_limit:
            # 删除最旧的缓存项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[key] = result
    
    def get_available_providers(self) -> List[str]:
        """获取可用的翻译提供商"""
        return [name for name, translator in self.translators.items() 
                if translator.is_available()]
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        logger.info("Translation cache cleared")


# 全局翻译服务实例
translation_service = TranslationService()
