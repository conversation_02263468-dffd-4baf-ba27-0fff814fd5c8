#!/usr/bin/env python3
"""
SteamPlus 修复版启动脚本

修复了系统托盘和 Steam 连接的问题。
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_qt_availability():
    """检查 Qt 可用性"""
    try:
        from PySide6.QtWidgets import QApplication, QSystemTrayIcon
        from PySide6.QtCore import Qt
        
        # 创建临时应用来测试系统托盘支持
        app = QApplication.instance()
        if not app:
            app = QApplication([])
        
        if not QSystemTrayIcon.isSystemTrayAvailable():
            print("⚠️  系统托盘不可用")
            return False
        
        print("✅ Qt 和系统托盘可用")
        return True
        
    except ImportError as e:
        print(f"❌ PySide6 不可用: {e}")
        return False
    except Exception as e:
        print(f"⚠️  Qt 检查失败: {e}")
        return False


async def start_with_ui_safe():
    """安全启动带界面模式"""
    print("🚀 启动 SteamPlus (带界面模式)")
    
    # 检查 Qt 可用性
    if not check_qt_availability():
        print("❌ 无法启动界面模式，切换到无界面模式")
        await start_headless_safe()
        return
    
    try:
        from steamplus.core import app
        
        print("📋 初始化应用...")
        await app.initialize()
        
        print("🖥️  启动系统托盘...")
        
        # 导入并启动托盘应用
        from steamplus.ui import tray_app
        
        if tray_app.start():
            print("✅ 系统托盘启动成功")
            print("💡 查看系统托盘图标以控制应用")
            
            # 运行 Qt 事件循环
            exit_code = tray_app.exec()
            print(f"🏁 应用退出，退出代码: {exit_code}")
        else:
            print("❌ 系统托盘启动失败")
            await start_headless_safe()
            
    except KeyboardInterrupt:
        print("\n⏹️  收到中断信号，正在停止...")
    except Exception as e:
        print(f"❌ 界面模式启动失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n🔄 尝试无界面模式...")
        await start_headless_safe()
    finally:
        try:
            from steamplus.core import app
            await app.stop()
        except:
            pass


async def start_headless_safe():
    """安全启动无界面模式"""
    print("🚀 启动 SteamPlus (无界面模式)")
    
    try:
        from steamplus.core import app
        
        print("📋 初始化应用...")
        await app.initialize()
        
        print("🌐 启动 API 服务器...")
        
        # 启动 API 服务器
        await app._start_api_server()
        
        print("✅ SteamPlus 已启动")
        print("📡 API 文档: http://localhost:50055")
        print("🔄 按 Ctrl+C 停止应用")
        
        # 保持运行
        while app.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  收到中断信号，正在停止...")
    except Exception as e:
        print(f"❌ 无界面模式启动失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            from steamplus.core import app
            await app.stop()
        except:
            pass


async def start_api_only_safe():
    """安全启动仅 API 模式"""
    print("🌐 启动 SteamPlus API 服务器")
    
    try:
        import uvicorn
        from steamplus.api.server import app
        from steamplus.service.settings import get_settings
        
        settings = get_settings()
        port = settings.general.api_port
        
        print(f"📡 API 服务器启动在: http://localhost:{port}")
        print(f"📖 API 文档: http://localhost:{port}")
        print("🔄 按 Ctrl+C 停止服务器")
        
        # 启动 uvicorn 服务器
        config = uvicorn.Config(
            app=app,
            host="127.0.0.1",
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        print("\n⏹️  API 服务器已停止")
    except Exception as e:
        print(f"❌ API 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()


def show_steam_help():
    """显示 Steam 连接帮助"""
    print("""
🎮 Steam 连接说明:

如果看到 "Steam debug port not found" 错误，请按以下步骤操作：

1. 关闭 Steam 客户端
2. 右键点击 Steam 快捷方式，选择"属性"
3. 在"目标"字段末尾添加: --remote-debugging-port=9222
   例如: "C:\\Program Files (x86)\\Steam\\Steam.exe" --remote-debugging-port=9222
4. 重新启动 Steam
5. 再次运行 SteamPlus

或者，您可以：
- 仅使用翻译 API 功能（不需要 Steam 连接）
- 使用 --api-only 模式启动

""")


def main():
    """主函数"""
    args = sys.argv[1:]
    
    print("🎮 SteamPlus - Steam 即时翻译工具 (修复版)")
    print("=" * 50)
    
    # 显示帮助
    if '--help' in args or len(args) == 0:
        print("""
用法: python start_fixed.py [选项]

选项:
    --ui, -u          带界面模式启动 (默认)
    --headless, -h    无界面模式启动
    --api-only, -a    仅启动 API 服务器
    --steam-help      显示 Steam 连接帮助
    --help            显示此帮助信息

修复内容:
    ✅ 修复了系统托盘图标问题
    ✅ 改进了 Steam 连接错误处理
    ✅ 添加了更好的错误恢复机制
    ✅ 提供了 Steam 连接帮助说明
        """)
        return
    
    if '--steam-help' in args:
        show_steam_help()
        return
    
    # 根据参数启动不同模式
    try:
        if '--headless' in args or '-h' in args:
            asyncio.run(start_headless_safe())
        elif '--api-only' in args or '-a' in args:
            asyncio.run(start_api_only_safe())
        else:
            # 默认尝试带界面模式
            asyncio.run(start_with_ui_safe())
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
